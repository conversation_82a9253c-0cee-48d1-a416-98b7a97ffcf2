/**
 * Bamboo Withdraw Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize withdraw page functionality
    initializeWithdrawPage();
});

function initializeWithdrawPage() {
    // Fee calculation
    setupFeeCalculation();
    
    // Form validation
    setupFormValidation();
    
    // Network selection handling
    setupNetworkSelection();
    
    // PIN input formatting
    setupPINInput();
    
    // USDT address validation
    setupAddressValidation();
    
    // Auto-dismiss alerts
    setupAlertAutoDismiss();
    
    // Form submission handling
    setupFormSubmission();
}

function setupFeeCalculation() {
    const amountInput = document.getElementById('amount');
    const withdrawalAmountSpan = document.getElementById('withdrawalAmount');
    const feeAmountSpan = document.getElementById('feeAmount');
    const netAmountSpan = document.getElementById('netAmount');
    
    if (!amountInput || !withdrawalAmountSpan || !feeAmountSpan || !netAmountSpan) {
        return; // Elements not found, probably withdrawal not available
    }
    
    function calculateFees() {
        const amount = parseFloat(amountInput.value) || 0;
        const feeAmount = (amount * withdrawalFeePercentage) / 100;
        const netAmount = amount - feeAmount;
        
        withdrawalAmountSpan.textContent = formatCurrency(amount);
        feeAmountSpan.textContent = formatCurrency(feeAmount);
        netAmountSpan.textContent = formatCurrency(netAmount);
        
        // Add visual feedback for amounts
        if (amount > 0) {
            withdrawalAmountSpan.parentElement.parentElement.classList.add('fade-in');
        }
    }
    
    amountInput.addEventListener('input', calculateFees);
    amountInput.addEventListener('change', calculateFees);
    
    // Initial calculation
    calculateFees();
}

function setupFormValidation() {
    const form = document.getElementById('withdrawalForm');
    if (!form) return;
    
    const amountInput = document.getElementById('amount');
    const pinInput = document.getElementById('withdrawal_pin');
    const addressInput = document.getElementById('usdt_address');
    
    // Amount validation
    amountInput.addEventListener('input', function() {
        const amount = parseFloat(this.value);
        const min = parseFloat(this.getAttribute('min'));
        const max = parseFloat(this.getAttribute('max'));
        
        if (amount < min) {
            this.setCustomValidity(`Minimum withdrawal amount is $${min.toFixed(2)}`);
            this.classList.add('is-invalid');
        } else if (amount > max) {
            this.setCustomValidity(`Maximum withdrawal amount is $${max.toFixed(2)}`);
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    // PIN validation
    pinInput.addEventListener('input', function() {
        const pin = this.value;
        
        // Remove non-numeric characters
        this.value = pin.replace(/\D/g, '');
        
        if (this.value.length !== 6) {
            this.setCustomValidity('PIN must be exactly 6 digits');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    // Address validation
    addressInput.addEventListener('input', function() {
        const address = this.value.trim();
        const selectedNetwork = document.querySelector('input[name="network"]:checked').value;
        
        if (validateUSDTAddress(address, selectedNetwork)) {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (address.length > 0) {
            this.setCustomValidity(`Invalid ${selectedNetwork} address format`);
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid', 'is-valid');
        }
    });
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
}

function setupNetworkSelection() {
    const networkRadios = document.querySelectorAll('input[name="network"]');
    const addressInput = document.getElementById('usdt_address');
    
    networkRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Clear address validation when network changes
            if (addressInput) {
                addressInput.classList.remove('is-valid', 'is-invalid');
                addressInput.setCustomValidity('');
                
                // Update placeholder
                const network = this.value;
                if (network === 'TRC20') {
                    addressInput.placeholder = 'Enter TRC20 address (starts with T...)';
                } else if (network === 'ERC20') {
                    addressInput.placeholder = 'Enter ERC20 address (starts with 0x...)';
                }
                
                // Revalidate if there's already an address
                if (addressInput.value.trim()) {
                    addressInput.dispatchEvent(new Event('input'));
                }
            }
        });
    });
}

function setupPINInput() {
    const pinInput = document.getElementById('withdrawal_pin');
    if (!pinInput) return;
    
    // Format PIN input
    pinInput.addEventListener('input', function(e) {
        // Only allow numbers
        let value = e.target.value.replace(/\D/g, '');
        
        // Limit to 6 digits
        if (value.length > 6) {
            value = value.substring(0, 6);
        }
        
        e.target.value = value;
    });
    
    // Prevent paste of non-numeric content
    pinInput.addEventListener('paste', function(e) {
        e.preventDefault();
        const paste = (e.clipboardData || window.clipboardData).getData('text');
        const numericPaste = paste.replace(/\D/g, '').substring(0, 6);
        this.value = numericPaste;
        this.dispatchEvent(new Event('input'));
    });
}

function setupAddressValidation() {
    const addressInput = document.getElementById('usdt_address');
    if (!addressInput) return;
    
    // Real-time address validation
    addressInput.addEventListener('input', function() {
        const address = this.value.trim();
        const selectedNetwork = document.querySelector('input[name="network"]:checked')?.value;
        
        if (!address) {
            this.classList.remove('is-valid', 'is-invalid');
            return;
        }
        
        if (validateUSDTAddress(address, selectedNetwork)) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
            showAddressValidationFeedback('valid', selectedNetwork);
        } else {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            showAddressValidationFeedback('invalid', selectedNetwork);
        }
    });
}

function showAddressValidationFeedback(status, network) {
    // Remove existing feedback
    const existingFeedback = document.getElementById('addressValidationFeedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    const addressInput = document.getElementById('usdt_address');
    const feedbackDiv = document.createElement('div');
    feedbackDiv.id = 'addressValidationFeedback';
    feedbackDiv.className = `form-text ${status === 'valid' ? 'text-success' : 'text-danger'}`;
    
    if (status === 'valid') {
        feedbackDiv.innerHTML = `<i class="bi bi-check-circle me-1"></i>Valid ${network} address format`;
    } else {
        feedbackDiv.innerHTML = `<i class="bi bi-x-circle me-1"></i>Invalid ${network} address format`;
    }
    
    addressInput.parentNode.insertBefore(feedbackDiv, addressInput.nextSibling.nextSibling);
}

function setupAlertAutoDismiss() {
    // Auto-dismiss success alerts after 5 seconds
    const successAlerts = document.querySelectorAll('.alert-success');
    successAlerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

function setupFormSubmission() {
    const form = document.getElementById('withdrawalForm');
    if (!form) return;
    
    const submitButton = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        if (form.checkValidity()) {
            // Show confirmation dialog
            const amount = document.getElementById('amount').value;
            const address = document.getElementById('usdt_address').value;
            const network = document.querySelector('input[name="network"]:checked').value;
            
            const confirmed = confirm(
                `Confirm Withdrawal:\n\n` +
                `Amount: $${parseFloat(amount).toFixed(2)}\n` +
                `Network: ${network}\n` +
                `Address: ${address.substring(0, 10)}...${address.substring(address.length - 10)}\n\n` +
                `Are you sure you want to proceed?`
            );
            
            if (!confirmed) {
                e.preventDefault();
                return;
            }
            
            // Show loading state
            submitButton.classList.add('btn-loading');
            submitButton.disabled = true;
            
            // The form will submit normally, but we show loading state
            setTimeout(() => {
                if (submitButton) {
                    submitButton.classList.remove('btn-loading');
                    submitButton.disabled = false;
                }
            }, 10000); // Remove loading state after 10 seconds as fallback
        }
    });
}

function validateUSDTAddress(address, network) {
    if (!address || !network) return false;
    
    if (network === 'TRC20') {
        // TRC20 addresses start with 'T' and are 34 characters long
        return /^T[A-Za-z0-9]{33}$/.test(address);
    } else if (network === 'ERC20') {
        // ERC20 addresses start with '0x' and are 42 characters long
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }
    
    return false;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 8000 : 4000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Utility function to check withdrawal requirements
function checkWithdrawalRequirements() {
    const form = document.getElementById('withdrawalForm');
    if (!form) {
        // Withdrawal form not available, requirements not met
        return false;
    }
    return true;
}

// Export functions for global access
window.validateUSDTAddress = validateUSDTAddress;
window.formatCurrency = formatCurrency;
window.showToast = showToast;
