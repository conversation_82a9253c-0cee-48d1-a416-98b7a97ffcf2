<?php
/**
 * Bamboo Web Application - User Withdraw Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Set page title for header
$page_title = 'Withdraw Funds';

// Get app settings
$app_name = getAppSetting('app_name', 'Bamboo');
$payment_password_label = getAppSetting('payment_password_label', 'Withdrawal PIN');

// Set page title
$page_title = 'Withdraw';

// Handle withdrawal request
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_withdrawal'])) {
    $amount = floatval($_POST['amount']);
    $withdrawal_pin = $_POST['withdrawal_pin'] ?? '';

    // Basic validation
    if ($amount <= 0) {
        $error_message = "Please enter a valid withdrawal amount";
    } elseif ($amount > $user['balance']) {
        $error_message = "Insufficient balance. Available: $" . number_format($user['balance'], 2);
    } elseif (empty($withdrawal_pin)) {
        $error_message = $payment_password_label . " is required";
    } else {
        // Create withdrawal transaction
        $transaction_data = [
            'user_id' => $user_id,
            'type' => 'withdrawal',
            'amount' => -$amount,
            'status' => 'pending',
            'description' => 'User withdrawal request',
            'created_at' => date('Y-m-d H:i:s')
        ];

        if (insertRecord('transactions', $transaction_data)) {
            // Update user balance
            $new_balance = $user['balance'] - $amount;
            updateRecord('users', ['balance' => $new_balance], 'id = ?', [$user_id]);

            $success_message = "Withdrawal request submitted successfully. You will receive your withdraw within an hour.";

            // Refresh user data
            $user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
        } else {
            $error_message = "Failed to process withdrawal request. Please try again.";
        }
    }
}

// Get withdrawal history
$withdrawal_history = fetchAll("SELECT * FROM transactions WHERE user_id = ? AND type = 'withdrawal' ORDER BY created_at DESC LIMIT 20", [$user_id]);

// Include header
include '../includes/user_header.php';
?>
<!-- Page Content -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Withdraw Form Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-cash-coin me-2"></i>Withdraw Funds
                </h5>
            </div>
            <div class="card-body">
                <!-- Total Balance Display -->
                <div class="balance-display mb-4">
                    <div class="text-center">
                        <h3 class="text-primary mb-1">$<?php echo number_format($user['balance'], 2); ?></h3>
                        <p class="text-muted mb-0">Total Balance</p>
                    </div>
                </div>

                <!-- Withdrawal Notice -->
                <div class="alert alert-info mb-4">
                    <i class="bi bi-info-circle me-2"></i>
                    You will receive your withdraw within an hour
                </div>

                <!-- Withdrawal Method -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Withdraw Method:</label>
                    <p class="text-muted mb-0">Withdrawal will be transferred to Exchange Wallet</p>
                </div>

                <!-- Withdrawal Form -->
                <form method="POST" action="">
                    <!-- Amount Input -->
                    <div class="mb-4">
                        <label for="amount" class="form-label fw-bold">Amount:</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number"
                                   class="form-control"
                                   id="amount"
                                   name="amount"
                                   step="0.01"
                                   min="0.01"
                                   max="<?php echo $user['balance']; ?>"
                                   placeholder="Enter withdrawal amount"
                                   required>
                        </div>
                    </div>

                    <!-- Withdrawal PIN -->
                    <div class="mb-4">
                        <label for="withdrawal_pin" class="form-label fw-bold"><?php echo htmlspecialchars($payment_password_label); ?>:</label>
                        <input type="password"
                               class="form-control"
                               id="withdrawal_pin"
                               name="withdrawal_pin"
                               placeholder="Enter your <?php echo strtolower($payment_password_label); ?>"
                               required>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" name="submit_withdrawal" class="btn btn-primary btn-lg">
                            <i class="bi bi-send me-2"></i>Submit Withdrawal Request
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- History Tab -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>Withdrawal History
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($withdrawal_history)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <p class="text-muted mt-2">No withdrawal history found</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($withdrawal_history as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('M j, Y g:i A', strtotime($transaction['created_at'])); ?></td>
                                        <td class="text-danger">$<?php echo number_format(abs($transaction['amount']), 2); ?></td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            $status_icon = '';
                                            switch ($transaction['status']) {
                                                case 'completed':
                                                    $status_class = 'success';
                                                    $status_icon = 'check-circle';
                                                    break;
                                                case 'pending':
                                                    $status_class = 'warning';
                                                    $status_icon = 'clock';
                                                    break;
                                                case 'failed':
                                                case 'cancelled':
                                                    $status_class = 'danger';
                                                    $status_icon = 'x-circle';
                                                    break;
                                                default:
                                                    $status_class = 'secondary';
                                                    $status_icon = 'question-circle';
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $status_class; ?>">
                                                <i class="bi bi-<?php echo $status_icon; ?> me-1"></i>
                                                <?php echo ucfirst($transaction['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($transaction['description'] ?? ''); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/user_footer.php'; ?>
