<?php
/**
 * Bamboo Web Application - User Deposit Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Get VIP level information
$vip_level = fetchRow("SELECT * FROM vip_levels WHERE level = ?", [$user['vip_level']]);

// Get deposit wallet addresses from settings
$usdt_wallet_trc20 = getAppSetting('deposit_wallet_trc20', 'TRC20 wallet not configured');
$usdt_wallet_erc20 = getAppSetting('deposit_wallet_erc20', 'ERC20 wallet not configured');
$min_deposit = getAppSetting('min_deposit_amount', 10);

// Get app settings for theme colors
$app_name = getAppSetting('app_name', 'Bamboo');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_deposit'])) {
    $amount = floatval($_POST['amount']);
    $transaction_hash = trim($_POST['transaction_hash']);
    $network = $_POST['network'];
    $manual_wallet = trim($_POST['manual_wallet'] ?? '');
    $screenshot = null;
    
    // Validate inputs
    if ($amount < $min_deposit) {
        $error_message = "Minimum deposit amount is $" . number_format($min_deposit, 2);
    } elseif (empty($transaction_hash)) {
        $error_message = "Transaction hash is required";
    } elseif (!in_array($network, ['TRC20', 'ERC20'])) {
        $error_message = "Invalid network selected";
    } else {
        // Handle file upload if provided
        if (isset($_FILES['screenshot']) && $_FILES['screenshot']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../../uploads/deposit_screenshots/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['screenshot']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $filename = 'deposit_' . $user_id . '_' . time() . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['screenshot']['tmp_name'], $upload_path)) {
                    $screenshot = 'uploads/deposit_screenshots/' . $filename;
                }
            }
        }
        
        try {
            // Create deposit transaction record
            $db = getDB();
            $stmt = $db->prepare("INSERT INTO transactions (user_id, type, amount, status, description, payment_method_details, screenshot_path) VALUES (?, 'deposit', ?, 'pending', ?, ?, ?)");
            
            $description = "Deposit via $network - Hash: $transaction_hash";

            // Determine wallet address (manual input takes priority)
            $wallet_address = !empty($manual_wallet) ? $manual_wallet : ($network === 'TRC20' ? $usdt_wallet_trc20 : $usdt_wallet_erc20);

            $payment_details = json_encode([
                'network' => $network,
                'transaction_hash' => $transaction_hash,
                'wallet_address' => $wallet_address,
                'manual_wallet_used' => !empty($manual_wallet)
            ]);
            
            $stmt->execute([$user_id, $amount, $description, $payment_details, $screenshot]);
            
            // Log activity
            logActivity($user_id, 'deposit_request', "Deposit request submitted: $" . number_format($amount, 2));
            
            $success_message = "Deposit request submitted successfully! Your deposit will be processed within 24 hours.";
            
            // Clear form data
            $_POST = [];
            
        } catch (Exception $e) {
            $error_message = "Error submitting deposit request. Please try again.";
            error_log("Deposit submission error: " . $e->getMessage());
        }
    }
}

// Get recent deposit history
$recent_deposits = fetchAll("SELECT * FROM transactions WHERE user_id = ? AND type = 'deposit' ORDER BY created_at DESC LIMIT 10", [$user_id]);

$page_title = "Deposit Funds";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo htmlspecialchars($app_name); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="deposit.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --primary-rgb: <?php echo implode(',', sscanf($primary_color, "#%02x%02x%02x")); ?>;
            --secondary-rgb: <?php echo implode(',', sscanf($secondary_color, "#%02x%02x%02x")); ?>;
        }

        /* Consistent white background for all pages */
        body {
            background-color: #ffffff !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/dashboard.php">
                <i class="bi bi-arrow-left me-2"></i>Deposit Funds
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bi bi-wallet2 me-1"></i>
                    Balance: $<?php echo number_format($user['balance'], 2); ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Deposit Form -->
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-plus-circle me-2"></i>Make a Deposit</h5>
                    </div>
                    <div class="card-body">
                        <!-- Instructions -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>Deposit Instructions</h6>
                            <ol class="mb-0">
                                <li>Select your preferred network (TRC20 or ERC20)</li>
                                <li>Copy the wallet address and send USDT to it</li>
                                <li>Enter the transaction details below</li>
                                <li>Upload a screenshot of the transaction (optional)</li>
                                <li>Submit your deposit request</li>
                            </ol>
                        </div>

                        <form method="POST" enctype="multipart/form-data" id="depositForm">
                            <!-- Network Selection -->
                            <div class="mb-3">
                                <label class="form-label">Select Network</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="network" id="trc20" value="TRC20" checked>
                                            <label class="form-check-label" for="trc20">
                                                <strong>TRC20 (Tron)</strong><br>
                                                <small class="text-muted">Lower fees, faster confirmation</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="network" id="erc20" value="ERC20">
                                            <label class="form-check-label" for="erc20">
                                                <strong>ERC20 (Ethereum)</strong><br>
                                                <small class="text-muted">Higher fees, standard network</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Wallet Address Display -->
                            <div class="mb-3">
                                <label class="form-label">Deposit Wallet Address</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="walletAddress" value="<?php echo $usdt_wallet_trc20; ?>" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyWalletAddress()">
                                        <i class="bi bi-clipboard"></i> Copy
                                    </button>
                                    <button class="btn btn-outline-primary" type="button" onclick="toggleManualWallet()">
                                        <i class="bi bi-pencil"></i> Manual
                                    </button>
                                </div>
                                <small class="text-muted">Send USDT only to this address</small>
                            </div>

                            <!-- Manual Wallet Input (Hidden by default) -->
                            <div class="mb-3" id="manualWalletSection" style="display: none;">
                                <label for="manual_wallet" class="form-label">Manual Wallet Address</label>
                                <input type="text" class="form-control" id="manual_wallet" name="manual_wallet"
                                       placeholder="Enter wallet address if different from above">
                                <small class="text-muted">Only use this if you need to specify a different wallet address</small>
                            </div>

                            <!-- Amount -->
                            <div class="mb-3">
                                <label for="amount" class="form-label">Deposit Amount (USDT)</label>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       min="<?php echo $min_deposit; ?>" step="0.01" required
                                       placeholder="Enter amount (min: $<?php echo number_format($min_deposit, 2); ?>)">
                            </div>

                            <!-- Transaction Hash -->
                            <div class="mb-3">
                                <label for="transaction_hash" class="form-label">Transaction Hash/ID</label>
                                <input type="text" class="form-control" id="transaction_hash" name="transaction_hash" required
                                       placeholder="Enter the transaction hash from your wallet">
                                <small class="text-muted">You can find this in your wallet's transaction history</small>
                            </div>

                            <!-- Screenshot Upload -->
                            <div class="mb-3">
                                <label for="screenshot" class="form-label">Transaction Screenshot (Optional)</label>
                                <input type="file" class="form-control" id="screenshot" name="screenshot" 
                                       accept="image/*">
                                <small class="text-muted">Upload a screenshot of your transaction for faster processing</small>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid">
                                <button type="submit" name="submit_deposit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-send me-2"></i>Submit Deposit Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Account Info -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="bi bi-person-circle me-2"></i>Account Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Username:</span>
                            <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>VIP Level:</span>
                            <span class="badge bg-primary"><?php echo $vip_level['name']; ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Current Balance:</span>
                            <strong class="text-success">$<?php echo number_format($user['balance'], 2); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Total Deposited:</span>
                            <span>$<?php echo number_format($user['total_deposited'], 2); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Deposit History -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Deposits</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_deposits)): ?>
                            <p class="text-muted text-center mb-0">No deposit history</p>
                        <?php else: ?>
                            <?php foreach ($recent_deposits as $deposit): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                                    <div>
                                        <div class="fw-bold">$<?php echo number_format($deposit['amount'], 2); ?></div>
                                        <small class="text-muted"><?php echo date('M j, Y', strtotime($deposit['created_at'])); ?></small>
                                    </div>
                                    <span class="badge bg-<?php echo $deposit['status'] === 'completed' ? 'success' : ($deposit['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                        <?php echo ucfirst($deposit['status']); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Customer Service Support -->
                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="bi bi-headset me-2"></i>Need Help?</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">Having trouble with your deposit? Our customer service team is here to help!</p>

                        <div class="row g-2">
                            <div class="col-md-6">
                                <a href="#" class="btn btn-outline-primary w-100" onclick="openCustomerService()">
                                    <i class="bi bi-chat-dots me-2"></i>Live Chat
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="#" class="btn btn-outline-success w-100" onclick="openWhatsApp()">
                                    <i class="bi bi-whatsapp me-2"></i>WhatsApp
                                </a>
                            </div>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i>
                                Support Hours: 24/7 Available
                            </small>
                        </div>

                        <div class="alert alert-info mt-3 mb-0">
                            <small>
                                <strong>Common Issues:</strong><br>
                                • Transaction not showing? Wait 10-30 minutes for network confirmation<br>
                                • Wrong network? Contact support immediately<br>
                                • Need faster processing? Upload transaction screenshot
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// Set additional JS for footer
$additional_js = '
    <script src="deposit.js"></script>
';
include '../includes/user_footer.php';
?>
