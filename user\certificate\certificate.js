/**
 * Bamboo Certificate Display Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize certificate page functionality
    initializeCertificatePage();
});

function initializeCertificatePage() {
    // Setup animations
    setupAnimations();

    // Setup tooltips
    setupTooltips();

    // Setup modal functionality
    setupModalFunctionality();

    // Setup progress bar animations
    setupProgressAnimations();

    // Setup certificate interactions
    setupCertificateInteractions();
}

function setupAnimations() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });

    // Add scale-in animation to achievement items
    const achievementItems = document.querySelectorAll('.achievement-item');
    achievementItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.05}s`;
        item.classList.add('scale-in');
    });

    // Animate status icon
    const statusIcon = document.querySelector('.status-icon');
    if (statusIcon) {
        setTimeout(() => {
            statusIcon.style.animation = statusIcon.classList.contains('success') ?
                'pulse-success 2s infinite' : 'pulse-warning 2s infinite';
        }, 500);
    }
}

function setupProgressAnimations() {
    // Animate progress bars after page load
    setTimeout(() => {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            bar.style.transition = 'width 2s ease-in-out';

            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });
    }, 800);

    // Add hover effects to requirement items
    const requirementItems = document.querySelectorAll('.requirement-item');
    requirementItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const progressBar = this.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.transform = 'scaleY(1.2)';
                progressBar.style.transition = 'transform 0.3s ease';
            }
        });

        item.addEventListener('mouseleave', function() {
            const progressBar = this.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.transform = 'scaleY(1)';
            }
        });
    });
}

function setupTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupModalFunctionality() {
    // Initialize share modal
    const shareModal = new bootstrap.Modal(document.getElementById('shareModal'));
    window.shareModal = shareModal;

    // Setup modal event listeners
    const shareModalElement = document.getElementById('shareModal');
    shareModalElement.addEventListener('show.bs.modal', function() {
        // Add entrance animation
        this.querySelector('.modal-dialog').style.animation = 'slideInDown 0.3s ease-out';
    });
}

function setupCertificateInteractions() {
    // Add hover effects to certificate image
    const certificateImage = document.querySelector('.certificate-image');
    if (certificateImage) {
        certificateImage.addEventListener('click', function() {
            // Create fullscreen overlay
            createFullscreenOverlay(this.src);
        });
    }

    // Add click-to-copy functionality for certificate ID
    const certificateDetails = document.querySelectorAll('.certificate-details p');
    certificateDetails.forEach(detail => {
        if (detail.textContent.includes('Certificate ID:')) {
            detail.style.cursor = 'pointer';
            detail.title = 'Click to copy Certificate ID';
            detail.addEventListener('click', function() {
                const certId = this.textContent.split(': ')[1];
                copyToClipboard(certId, 'Certificate ID copied to clipboard!');
            });
        }
    });
}

function shareCertificate() {
    if (window.certificateData && window.certificateData.isEligible) {
        window.shareModal.show();
    } else {
        showToast('Certificate not available yet. Complete the requirements first!', 'warning');
    }
}

function shareViaWhatsApp() {
    const data = window.certificateData;
    const message = `🎉 I just earned my official ${data.companyName} ${data.appName} Certificate!

🏆 Certified Member Achievement Unlocked!
📜 Certificate ID: ${data.certificateId}
👤 Certified Member: ${data.username}

✅ Tasks Mastery
✅ Commission Excellence
✅ VIP Status Achieved
✅ Platform Expertise

Join me on ${data.appName} and start your journey to certification!

#${data.appName}Certified #Achievement #Success`;

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareViaFacebook() {
    const data = window.certificateData;
    const shareUrl = window.location.href;
    const message = `🎉 Just earned my ${data.companyName} ${data.appName} Certificate! Certified Member Achievement Unlocked! 🏆`;

    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(message)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
}

function shareViaTwitter() {
    const data = window.certificateData;
    const message = `🎉 Just earned my ${data.companyName} ${data.appName} Certificate! 🏆 Certified Member Achievement Unlocked! #${data.appName}Certified #Achievement #Success`;

    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
}

function copyShareText() {
    const data = window.certificateData;
    const shareText = `🎉 I just earned my official ${data.companyName} ${data.appName} Certificate!

🏆 Certified Member Achievement Unlocked!
📜 Certificate ID: ${data.certificateId}
👤 Certified Member: ${data.username}

✅ Tasks Mastery
✅ Commission Excellence
✅ VIP Status Achieved
✅ Platform Expertise

This certificate represents my dedication and success on the ${data.appName} platform!

#${data.appName}Certified #Achievement #Success`;

    copyToClipboard(shareText, 'Share text copied to clipboard!');
    window.shareModal.hide();
}

function copyToClipboard(text, successMessage = 'Copied to clipboard!') {
    navigator.clipboard.writeText(text).then(() => {
        showToast(successMessage, 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast(successMessage, 'success');
    });
}

function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 8000 : 5000
    });

    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Export functions for global access
window.shareCertificate = shareCertificate;
window.shareViaWhatsApp = shareViaWhatsApp;
window.shareViaFacebook = shareViaFacebook;
window.shareViaTwitter = shareViaTwitter;
window.copyShareText = copyShareText;

function setupProgressAnimations() {
    // Animate progress bars after page load
    setTimeout(() => {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            bar.style.transition = 'width 2s ease-in-out';
            
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });
    }, 800);
    
    // Add hover effects to requirement items
    const requirementItems = document.querySelectorAll('.requirement-item');
    requirementItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const progressBar = this.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.transform = 'scaleY(1.2)';
                progressBar.style.transition = 'transform 0.3s ease';
            }
        });
        
        item.addEventListener('mouseleave', function() {
            const progressBar = this.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.transform = 'scaleY(1)';
            }
        });
    });
}

function setupTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupModalFunctionality() {
    // Initialize share modal
    const shareModal = new bootstrap.Modal(document.getElementById('shareModal'));
    window.shareModal = shareModal;
    
    // Setup modal event listeners
    const shareModalElement = document.getElementById('shareModal');
    shareModalElement.addEventListener('show.bs.modal', function() {
        // Add entrance animation
        this.querySelector('.modal-dialog').style.animation = 'slideInDown 0.3s ease-out';
    });
}

function setupCertificateInteractions() {
    // Add hover effects to certificate image
    const certificateImage = document.querySelector('.certificate-image');
    if (certificateImage) {
        certificateImage.addEventListener('click', function() {
            // Create fullscreen overlay
            createFullscreenOverlay(this.src);
        });
    }
    
    // Add click-to-copy functionality for certificate ID
    const certificateDetails = document.querySelectorAll('.certificate-details p');
    certificateDetails.forEach(detail => {
        if (detail.textContent.includes('Certificate ID:')) {
            detail.style.cursor = 'pointer';
            detail.title = 'Click to copy Certificate ID';
            detail.addEventListener('click', function() {
                const certId = this.textContent.split(': ')[1];
                copyToClipboard(certId, 'Certificate ID copied to clipboard!');
            });
        }
    });
}

function createFullscreenOverlay(imageSrc) {
    // Create overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        cursor: pointer;
    `;
    
    // Create image
    const img = document.createElement('img');
    img.src = imageSrc;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        border-radius: 12px;
        box-shadow: 0 0 50px rgba(255, 255, 255, 0.3);
        animation: zoomIn 0.3s ease-out;
    `;
    
    // Add close button
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="bi bi-x-lg"></i>';
    closeBtn.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 1.5rem;
        padding: 10px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
    `;
    
    closeBtn.addEventListener('mouseenter', function() {
        this.style.background = 'rgba(255, 255, 255, 0.3)';
        this.style.transform = 'scale(1.1)';
    });
    
    closeBtn.addEventListener('mouseleave', function() {
        this.style.background = 'rgba(255, 255, 255, 0.2)';
        this.style.transform = 'scale(1)';
    });
    
    // Close overlay on click
    overlay.addEventListener('click', function() {
        document.body.removeChild(overlay);
    });
    
    closeBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        document.body.removeChild(overlay);
    });
    
    // Append elements
    overlay.appendChild(img);
    overlay.appendChild(closeBtn);
    document.body.appendChild(overlay);
    
    // Add zoom animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes zoomIn {
            from { transform: scale(0.5); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
}

function shareCertificate() {
    if (window.certificateData && window.certificateData.isEligible) {
        window.shareModal.show();
    } else {
        showToast('Certificate not available yet. Complete the requirements first!', 'warning');
    }
}

function shareViaWhatsApp() {
    const data = window.certificateData;
    const message = `🎉 I just earned my official ${data.companyName} ${data.appName} Certificate! 

🏆 Certified Member Achievement Unlocked!
📜 Certificate ID: ${data.certificateId}
👤 Certified Member: ${data.username}

✅ Tasks Mastery
✅ Commission Excellence  
✅ VIP Status Achieved
✅ Platform Expertise

Join me on ${data.appName} and start your journey to certification!

#${data.appName}Certified #Achievement #Success`;
    
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareViaFacebook() {
    const data = window.certificateData;
    const shareUrl = window.location.href;
    const message = `🎉 Just earned my ${data.companyName} ${data.appName} Certificate! Certified Member Achievement Unlocked! 🏆`;
    
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(message)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
}

function shareViaTwitter() {
    const data = window.certificateData;
    const message = `🎉 Just earned my ${data.companyName} ${data.appName} Certificate! 🏆 Certified Member Achievement Unlocked! #${data.appName}Certified #Achievement #Success`;
    
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
}

function copyShareText() {
    const data = window.certificateData;
    const shareText = `🎉 I just earned my official ${data.companyName} ${data.appName} Certificate!

🏆 Certified Member Achievement Unlocked!
📜 Certificate ID: ${data.certificateId}
👤 Certified Member: ${data.username}

✅ Tasks Mastery
✅ Commission Excellence  
✅ VIP Status Achieved
✅ Platform Expertise

This certificate represents my dedication and success on the ${data.appName} platform!

#${data.appName}Certified #Achievement #Success`;
    
    copyToClipboard(shareText, 'Share text copied to clipboard!');
    window.shareModal.hide();
}

function copyToClipboard(text, successMessage = 'Copied to clipboard!') {
    navigator.clipboard.writeText(text).then(() => {
        showToast(successMessage, 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast(successMessage, 'success');
    });
}

function animateAchievementNumbers() {
    const achievementNumbers = document.querySelectorAll('.achievement-details h5');
    
    achievementNumbers.forEach(element => {
        const finalValue = element.textContent.replace(/[^0-9.]/g, '');
        if (finalValue && !isNaN(finalValue)) {
            const startValue = 0;
            const duration = 2000;
            const startTime = Date.now();
            
            function updateNumber() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentValue = startValue + (finalValue - startValue) * progress;
                
                if (element.textContent.includes('$')) {
                    element.textContent = '$' + currentValue.toFixed(2);
                } else if (element.textContent.includes('VIP')) {
                    element.textContent = 'VIP ' + Math.floor(currentValue);
                } else {
                    element.textContent = Math.floor(currentValue);
                }
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }
            
            updateNumber();
        }
    });
}

function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 8000 : 5000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Add custom CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInDown {
        from { transform: translate3d(0, -100%, 0); opacity: 0; }
        to { transform: translate3d(0, 0, 0); opacity: 1; }
    }
    
    @keyframes numberPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); color: #28a745; }
        100% { transform: scale(1); }
    }
    
    @keyframes certificateGlow {
        0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
        50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
        100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
    }
`;
document.head.appendChild(style);

// Start achievement number animation after page load
setTimeout(() => {
    animateAchievementNumbers();
}, 1000);

// Add certificate glow effect if eligible
if (window.certificateData && window.certificateData.isEligible) {
    setTimeout(() => {
        const certificateCard = document.querySelector('.certificate-card');
        if (certificateCard) {
            certificateCard.style.animation = 'certificateGlow 3s ease-in-out infinite';
        }
    }, 2000);
}

// Export functions for global access
window.shareCertificate = shareCertificate;
window.shareViaWhatsApp = shareViaWhatsApp;
window.shareViaFacebook = shareViaFacebook;
window.shareViaTwitter = shareViaTwitter;
window.copyShareText = copyShareText;
