<?php
/**
 * Bamboo Web Application - User Header Component
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Get app settings for consistent theming
$app_name = getAppSetting('app_name', 'Bamboo');
$app_logo = getAppSetting('app_logo', '');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Get user information if not already loaded
if (!isset($user) && isset($_SESSION['user_id'])) {
    $user = fetchRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) : 'Dashboard'; ?> - <?php echo htmlspecialchars($app_name); ?></title>

    <!-- Favicon -->
    <?php
    $favicon_path = BASE_URL . 'assets/images/favicon.ico';
    $favicon_exists = file_exists(__DIR__ . '/../../assets/images/favicon.ico') && filesize(__DIR__ . '/../../assets/images/favicon.ico') > 100;
    ?>
    <?php if ($favicon_exists): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>">
    <?php else: ?>
        <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><rect width='32' height='32' fill='<?php echo urlencode($primary_color); ?>'/><text x='16' y='20' font-family='Arial' font-size='18' fill='white' text-anchor='middle'>B</text></svg>">
    <?php endif; ?>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo BASE_URL; ?>assets/css/user-dashboard.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --primary-rgb: <?php echo implode(',', sscanf($primary_color, "#%02x%02x%02x")); ?>;
            --secondary-rgb: <?php echo implode(',', sscanf($secondary_color, "#%02x%02x%02x")); ?>;
        }
        
        /* Consistent white background for all pages */
        body {
            background-color: #ffffff !important;
        }
        
        .main-content {
            background-color: #ffffff;
            min-height: calc(100vh - 140px);
        }
        
        /* Remove dark backgrounds from cards */
        .card, .navigation-card, .stat-card {
            background-color: #ffffff !important;
            border: 1px solid #e9ecef;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
    
    <?php if (isset($additional_css)): ?>
        <?php echo $additional_css; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <?php
                        // Logo implementation matching admin sidebar
                        $logo_path = '';
                        if ($app_logo) {
                            // Check if the logo path already includes the full URL
                            if (strpos($app_logo, 'http') === 0) {
                                $logo_path = $app_logo;
                            } else {
                                $logo_path = BASE_URL . 'uploads/logos/' . $app_logo;
                            }

                            // Verify file exists
                            $full_logo_path = __DIR__ . '/../../uploads/logos/' . $app_logo;
                            if (!file_exists($full_logo_path)) {
                                $logo_path = '';
                            }
                        }
                        $file_extension = $app_logo ? strtolower(pathinfo($app_logo, PATHINFO_EXTENSION)) : '';
                        ?>
                        <?php if ($logo_path): ?>
                            <!-- Show logo only (no text) -->
                            <?php if ($file_extension === 'svg'): ?>
                                <div class="header-logo-svg">
                                    <object data="<?php echo $logo_path; ?>" type="image/svg+xml" class="header-logo" style="height: 40px; width: auto;">
                                        <img src="<?php echo $logo_path; ?>" alt="<?php echo htmlspecialchars($app_name); ?>" class="header-logo" style="height: 40px; width: auto;">
                                    </object>
                                </div>
                            <?php else: ?>
                                <img src="<?php echo $logo_path; ?>" alt="<?php echo htmlspecialchars($app_name); ?>" class="header-logo" style="height: 40px; width: auto;">
                            <?php endif; ?>
                        <?php else: ?>
                            <!-- Show text name only (no logo) -->
                            <h1 class="header-title mb-0"><?php echo htmlspecialchars($app_name); ?></h1>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-end">
                        <!-- User Info -->
                        <div class="user-info d-flex align-items-center me-3">
                            <div class="user-avatar me-2">
                                <?php if (!empty($user['avatar_url'])): ?>
                                    <img src="<?php echo BASE_URL . $user['avatar_url']; ?>" alt="Avatar" class="rounded-circle">
                                <?php else: ?>
                                    <div class="avatar-initials">
                                        <?php echo strtoupper(substr($user['username'] ?? 'U', 0, 1)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div>
                                <div class="user-name"><?php echo htmlspecialchars($user['username'] ?? 'User'); ?></div>
                                <div class="user-balance">Balance: $<?php echo number_format($user['balance'] ?? 0, 2); ?></div>
                            </div>
                        </div>
                        
                        <!-- User Menu -->
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userMenuDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuDropdown">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/profile/profile.php"><i class="bi bi-person me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/settings/settings.php"><i class="bi bi-gear me-2"></i>Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/logout/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
