/* Bamboo VIP Levels Page Styles */

:root {
    --primary-color: #6f42c1;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gold-color: #ffd700;
    --silver-color: #c0c0c0;
    --bronze-color: #cd7f32;
    --border-radius: 12px;
    --box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    background: #ffffff;
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a32a3 100%) !important;
}

.navbar-brand {
    font-weight: 600;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: translateX(-5px);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Current VIP Card */
.current-vip-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid var(--gold-color);
    position: relative;
}

.current-vip-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--gold-color) 0%, #ffed4e 50%, var(--gold-color) 100%);
}

.vip-icon-container {
    position: relative;
}

.vip-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid var(--gold-color);
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.vip-icon-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--gold-color) 0%, #ffed4e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.vip-icon-placeholder i {
    font-size: 2rem;
    color: white;
}

.progress-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.progress-item:last-child {
    border-bottom: none;
}

/* Next VIP Card */
.next-vip-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%) !important;
}

.requirement-item {
    margin-bottom: 1rem;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

.upgrade-benefits {
    background: rgba(40, 167, 69, 0.05);
    padding: 1rem;
    border-radius: var(--border-radius);
    border-left: 2px solid var(--success-color);
}

.benefit-comparison {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.benefit-comparison:last-child {
    margin-bottom: 0;
}

.benefit-comparison .current {
    color: var(--secondary-color);
    min-width: 60px;
    text-align: center;
}

.benefit-comparison .upgrade {
    min-width: 60px;
    text-align: center;
}

/* VIP Levels Comparison */
.vip-level-card {
    padding: 1.5rem;
    border-right: 1px solid #e9ecef;
    transition: var(--transition);
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.vip-level-card:last-child {
    border-right: none;
}

.vip-level-card:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateY(-5px);
}

.vip-level-card.current-level {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid var(--warning-color);
    transform: scale(1.02);
}

.vip-level-card.current-level::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--warning-color) 0%, #ffed4e 50%, var(--warning-color) 100%);
}

.vip-level-card.completed-level {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    opacity: 0.8;
}

.vip-level-header {
    text-align: center;
    margin-bottom: 1rem;
}

.vip-level-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 0.5rem;
    border: 2px solid var(--primary-color);
}

.vip-level-icon-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a32a3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    color: white;
}

.vip-level-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.vip-requirement {
    text-align: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: rgba(111, 66, 193, 0.1);
    border-radius: var(--border-radius);
    color: var(--primary-color);
}

.vip-benefits {
    list-style: none;
    padding: 0;
    margin-bottom: 1rem;
}

.vip-benefits li {
    padding: 0.25rem 0;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.vip-benefits li i {
    color: var(--success-color);
    margin-right: 0.5rem;
    font-size: 0.8rem;
}

.vip-description {
    text-align: center;
    font-style: italic;
}

/* Info Card */
.info-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 50px;
    font-weight: 500;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #545b62 100%) !important;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1aa179 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a32a3 100%);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a32a3 0%, #4c2a85 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

/* Modal Styles */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.6s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.scale-in {
    animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Pulse animation for current VIP */
.current-vip-card .vip-icon,
.current-vip-card .vip-icon-placeholder {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 4px 20px rgba(255, 215, 0, 0.6); }
    100% { box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3); }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .vip-level-card {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        margin-bottom: 1rem;
    }
    
    .vip-level-card:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .current-vip-card .row {
        text-align: center;
    }
    
    .current-vip-card .col-md-9 {
        margin-top: 1rem;
    }
    
    .benefit-comparison {
        font-size: 0.8rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .benefit-comparison .bi-arrow-right {
        margin: 0 0.5rem;
    }
    
    .upgrade-benefits {
        padding: 0.75rem;
    }
    
    .vip-icon,
    .vip-icon-placeholder {
        width: 60px;
        height: 60px;
    }
    
    .vip-icon-placeholder i {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .vip-level-card {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
    }
    
    .progress-item {
        font-size: 0.9rem;
    }
    
    .vip-benefits li {
        font-size: 0.8rem;
    }
    
    .benefit-comparison {
        font-size: 0.75rem;
    }
    
    .current-vip-card .col-md-3,
    .current-vip-card .col-md-9 {
        margin-bottom: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .current-vip-card {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        color: #ffffff;
    }
    
    .next-vip-card {
        background: linear-gradient(135deg, #3d3d3d 0%, #2d2d2d 100%);
        color: #ffffff;
    }
    
    .vip-level-card {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        color: #ffffff;
    }
    
    .vip-level-card.current-level {
        background: linear-gradient(135deg, #4a4a2a 0%, #5a5a3a 100%);
    }
    
    .vip-level-card.completed-level {
        background: linear-gradient(135deg, #2a4a2a 0%, #3a5a3a 100%);
    }
    
    .info-card {
        background: linear-gradient(135deg, #2a3a4a 0%, #2d2d2d 100%);
        color: #ffffff;
    }
    
    .progress {
        background-color: #555;
    }
    
    .upgrade-benefits {
        background: rgba(40, 167, 69, 0.2);
    }
    
    .vip-requirement {
        background: rgba(111, 66, 193, 0.2);
        color: #bb86fc;
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }
    
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
        break-inside: avoid;
    }
    
    .vip-level-card {
        border: 1px solid #dee2e6;
        margin-bottom: 1rem;
    }
}
