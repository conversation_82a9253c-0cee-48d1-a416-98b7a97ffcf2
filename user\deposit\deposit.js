/**
 * Bamboo Deposit Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize deposit page functionality
    initializeDepositPage();
});

function initializeDepositPage() {
    // Network selection handling
    setupNetworkSelection();
    
    // Form validation
    setupFormValidation();
    
    // Copy wallet address functionality
    setupWalletAddressCopy();
    
    // File upload preview
    setupFileUploadPreview();
    
    // Auto-dismiss alerts
    setupAlertAutoDismiss();
    
    // Form submission handling
    setupFormSubmission();
}

function setupNetworkSelection() {
    const trc20Radio = document.getElementById('trc20');
    const erc20Radio = document.getElementById('erc20');
    const walletAddressInput = document.getElementById('walletAddress');
    
    // Wallet addresses (these should match the PHP values)
    const walletAddresses = {
        'TRC20': walletAddressInput.value, // Default TRC20 address
        'ERC20': walletAddressInput.getAttribute('data-erc20') || 'ERC20 wallet not configured'
    };
    
    // Update wallet address when network changes
    function updateWalletAddress() {
        const selectedNetwork = document.querySelector('input[name="network"]:checked').value;
        walletAddressInput.value = walletAddresses[selectedNetwork];
        
        // Add animation effect
        walletAddressInput.style.transform = 'scale(1.02)';
        setTimeout(() => {
            walletAddressInput.style.transform = 'scale(1)';
        }, 200);
    }
    
    trc20Radio.addEventListener('change', updateWalletAddress);
    erc20Radio.addEventListener('change', updateWalletAddress);
    
    // Set ERC20 address from PHP if available
    fetch('get_wallet_addresses.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                walletAddresses['ERC20'] = data.erc20_address;
                updateWalletAddress();
            }
        })
        .catch(error => console.log('Could not fetch ERC20 address'));
}

function setupFormValidation() {
    const form = document.getElementById('depositForm');
    const amountInput = document.getElementById('amount');
    const transactionHashInput = document.getElementById('transaction_hash');
    
    // Real-time amount validation
    amountInput.addEventListener('input', function() {
        const amount = parseFloat(this.value);
        const minDeposit = parseFloat(this.getAttribute('min'));
        
        if (amount < minDeposit) {
            this.setCustomValidity(`Minimum deposit amount is $${minDeposit.toFixed(2)}`);
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    // Transaction hash validation
    transactionHashInput.addEventListener('input', function() {
        const hash = this.value.trim();
        
        if (hash.length < 10) {
            this.setCustomValidity('Transaction hash seems too short');
            this.classList.add('is-invalid');
        } else if (hash.length > 100) {
            this.setCustomValidity('Transaction hash seems too long');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
}

function copyWalletAddress() {
    const walletAddressInput = document.getElementById('walletAddress');
    const copyButton = document.querySelector('button[onclick="copyWalletAddress()"]');
    
    // Select and copy the text
    walletAddressInput.select();
    walletAddressInput.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        
        // Visual feedback
        const originalText = copyButton.innerHTML;
        copyButton.innerHTML = '<i class="bi bi-check"></i> Copied!';
        copyButton.classList.remove('btn-outline-secondary');
        copyButton.classList.add('btn-success');
        
        setTimeout(() => {
            copyButton.innerHTML = originalText;
            copyButton.classList.remove('btn-success');
            copyButton.classList.add('btn-outline-secondary');
        }, 2000);
        
        // Show toast notification
        showToast('Wallet address copied to clipboard!', 'success');
        
    } catch (err) {
        console.error('Failed to copy: ', err);
        showToast('Failed to copy address. Please copy manually.', 'error');
    }
}

function setupWalletAddressCopy() {
    // Modern clipboard API fallback
    if (navigator.clipboard) {
        window.copyWalletAddress = function() {
            const walletAddressInput = document.getElementById('walletAddress');
            const copyButton = document.querySelector('button[onclick="copyWalletAddress()"]');
            
            navigator.clipboard.writeText(walletAddressInput.value).then(() => {
                // Visual feedback
                const originalText = copyButton.innerHTML;
                copyButton.innerHTML = '<i class="bi bi-check"></i> Copied!';
                copyButton.classList.remove('btn-outline-secondary');
                copyButton.classList.add('btn-success');
                
                setTimeout(() => {
                    copyButton.innerHTML = originalText;
                    copyButton.classList.remove('btn-success');
                    copyButton.classList.add('btn-outline-secondary');
                }, 2000);
                
                showToast('Wallet address copied to clipboard!', 'success');
            }).catch(err => {
                console.error('Failed to copy: ', err);
                showToast('Failed to copy address. Please copy manually.', 'error');
            });
        };
    }
}

function setupFileUploadPreview() {
    const fileInput = document.getElementById('screenshot');
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        
        if (file) {
            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                showToast('Please select a valid image file (JPG, PNG, GIF)', 'error');
                this.value = '';
                return;
            }
            
            // Validate file size (max 5MB)
            const maxSize = 5 * 1024 * 1024; // 5MB
            if (file.size > maxSize) {
                showToast('File size must be less than 5MB', 'error');
                this.value = '';
                return;
            }
            
            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                showImagePreview(e.target.result, file.name);
            };
            reader.readAsDataURL(file);
        }
    });
}

function showImagePreview(src, filename) {
    // Remove existing preview
    const existingPreview = document.getElementById('imagePreview');
    if (existingPreview) {
        existingPreview.remove();
    }
    
    // Create preview element
    const previewDiv = document.createElement('div');
    previewDiv.id = 'imagePreview';
    previewDiv.className = 'mt-2 p-2 border rounded';
    previewDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <img src="${src}" alt="Preview" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
            <div class="ms-2">
                <div class="fw-bold">${filename}</div>
                <small class="text-muted">Image preview</small>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="removeImagePreview()">
                <i class="bi bi-x"></i>
            </button>
        </div>
    `;
    
    // Insert after file input
    const fileInput = document.getElementById('screenshot');
    fileInput.parentNode.insertBefore(previewDiv, fileInput.nextSibling);
}

function removeImagePreview() {
    const preview = document.getElementById('imagePreview');
    const fileInput = document.getElementById('screenshot');
    
    if (preview) {
        preview.remove();
    }
    
    fileInput.value = '';
}

function setupAlertAutoDismiss() {
    // Auto-dismiss success alerts after 5 seconds
    const successAlerts = document.querySelectorAll('.alert-success');
    successAlerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

function setupFormSubmission() {
    const form = document.getElementById('depositForm');
    const submitButton = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        if (form.checkValidity()) {
            // Show loading state
            submitButton.classList.add('btn-loading');
            submitButton.disabled = true;
            
            // The form will submit normally, but we show loading state
            setTimeout(() => {
                if (submitButton) {
                    submitButton.classList.remove('btn-loading');
                    submitButton.disabled = false;
                }
            }, 10000); // Remove loading state after 10 seconds as fallback
        }
    });
}

function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 8000 : 4000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// Utility function to validate USDT address
function validateUSDTAddress(address, network) {
    if (network === 'TRC20') {
        // TRC20 addresses start with 'T' and are 34 characters long
        return /^T[A-Za-z0-9]{33}$/.test(address);
    } else if (network === 'ERC20') {
        // ERC20 addresses start with '0x' and are 42 characters long
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }
    return false;
}

// Manual wallet toggle functionality
function toggleManualWallet() {
    const manualSection = document.getElementById('manualWalletSection');
    const toggleButton = document.querySelector('button[onclick="toggleManualWallet()"]');

    if (manualSection.style.display === 'none') {
        manualSection.style.display = 'block';
        toggleButton.innerHTML = '<i class="bi bi-x"></i> Cancel';
        toggleButton.classList.remove('btn-outline-primary');
        toggleButton.classList.add('btn-outline-danger');

        // Focus on manual input
        document.getElementById('manual_wallet').focus();
    } else {
        manualSection.style.display = 'none';
        toggleButton.innerHTML = '<i class="bi bi-pencil"></i> Manual';
        toggleButton.classList.remove('btn-outline-danger');
        toggleButton.classList.add('btn-outline-primary');

        // Clear manual input
        document.getElementById('manual_wallet').value = '';
    }
}

// Customer service integration functions
function openCustomerService() {
    // Get customer service settings from admin
    const customerServiceUrl = 'https://tawk.to/chat/your-chat-id'; // This should come from admin settings

    // Open in new window
    window.open(customerServiceUrl, 'customerService', 'width=400,height=600,scrollbars=yes,resizable=yes');

    showToast('Opening customer service chat...', 'info');
}

function openWhatsApp() {
    // Get WhatsApp number from admin settings
    const whatsappNumber = '+1234567890'; // This should come from admin settings
    const message = encodeURIComponent('Hello, I need help with my deposit on ' + window.location.hostname);

    // Open WhatsApp
    const whatsappUrl = `https://wa.me/${whatsappNumber.replace('+', '')}?text=${message}`;
    window.open(whatsappUrl, '_blank');

    showToast('Opening WhatsApp support...', 'success');
}

// Export functions for global access
window.copyWalletAddress = copyWalletAddress;
window.removeImagePreview = removeImagePreview;
window.toggleManualWallet = toggleManualWallet;
window.openCustomerService = openCustomerService;
window.openWhatsApp = openWhatsApp;
