/* Bamboo Transactions Page Styles */

:root {
    --primary-color: #17a2b8;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: translateX(-5px);
}

/* Statistics Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: 1px solid rgba(0,0,0,0.125);
}

/* Statistics Cards Gradients */
.card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.card.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.card.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--info-color);
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1aa179 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Table Styles */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table-hover tbody tr:hover {
    background-color: rgba(23, 162, 184, 0.05);
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid #f8f9fa;
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 50px;
    font-weight: 500;
}

.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

/* Empty State */
.display-1 {
    font-size: 4rem;
    opacity: 0.3;
}

/* Pagination */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: var(--primary-color);
}

/* Modal Styles */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Transaction Amount Colors */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading States */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .navbar-text {
        font-size: 0.9em;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .card-body.p-0 .table td {
        padding: 0.5rem 0.25rem;
    }
    
    .badge {
        font-size: 0.7em;
        padding: 0.4em 0.6em;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-header h6 {
        font-size: 0.9rem;
    }
    
    .row.g-3 > * {
        margin-bottom: 1rem;
    }
    
    .col-md-3, .col-md-2 {
        width: 100%;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: var(--border-radius) !important;
        margin-bottom: 0.25rem;
    }
    
    .table thead {
        display: none;
    }
    
    .table tbody tr {
        display: block;
        border: 1px solid #dee2e6;
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
        padding: 1rem;
    }
    
    .table tbody td {
        display: block;
        text-align: left !important;
        border: none;
        padding: 0.25rem 0;
    }
    
    .table tbody td:before {
        content: attr(data-label) ": ";
        font-weight: bold;
        display: inline-block;
        width: 100px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .card-header.bg-light {
        background-color: #3d3d3d !important;
        color: #ffffff;
    }
    
    .table {
        color: #ffffff;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
    
    .form-control, .form-select {
        background-color: #3d3d3d;
        border-color: #555;
        color: #ffffff;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #3d3d3d;
        border-color: var(--info-color);
        color: #ffffff;
    }
    
    .modal-content {
        background-color: #2d2d2d;
        color: #ffffff;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .card-header .btn-group {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background-color: white !important;
    }
    
    .table {
        font-size: 0.8rem;
    }
}
