<?php
/**
 * Bamboo Web Application - User Team Management Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Get app settings for theme colors
$app_name = getAppSetting('app_name', 'Bamboo');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Get referral statistics
$team_stats = [
    'total_referrals' => $user['referral_count'],
    'direct_referrals' => getRecordCount('users', 'invited_by = ? AND status = "active"', [$user_id]),
    'total_team_commission' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'commission' AND status = 'completed'", [$user_id]),
    'this_month_commission' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'commission' AND status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())", [$user_id])
];

// Get direct referrals (Level 1)
$direct_referrals = fetchAll("
    SELECT u.*, vl.name as vip_name,
           COALESCE(SUM(CASE WHEN t.type = 'commission' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) as total_commission_generated
    FROM users u 
    LEFT JOIN vip_levels vl ON u.vip_level = vl.level
    LEFT JOIN transactions t ON u.id = t.user_id
    WHERE u.invited_by = ? AND u.status = 'active'
    GROUP BY u.id
    ORDER BY u.created_at DESC
", [$user_id]);

// Get team levels breakdown (up to 3 levels)
function getTeamLevels($user_id, $level = 1, $max_level = 3) {
    if ($level > $max_level) return [];
    
    $direct_team = fetchAll("SELECT id, username, created_at, balance, total_commission_earned FROM users WHERE invited_by = ? AND status = 'active'", [$user_id]);
    
    $result = [];
    foreach ($direct_team as $member) {
        $member['level'] = $level;
        $result[] = $member;
        
        // Get sub-team recursively
        $sub_team = getTeamLevels($member['id'], $level + 1, $max_level);
        $result = array_merge($result, $sub_team);
    }
    
    return $result;
}

$all_team_members = getTeamLevels($user_id);

// Calculate team statistics by level
$level_stats = [
    1 => ['count' => 0, 'commission' => 0],
    2 => ['count' => 0, 'commission' => 0],
    3 => ['count' => 0, 'commission' => 0]
];

foreach ($all_team_members as $member) {
    $level = $member['level'];
    if (isset($level_stats[$level])) {
        $level_stats[$level]['count']++;
        $level_stats[$level]['commission'] += $member['total_commission_earned'];
    }
}

// Get commission rates from app settings (default values if not set)
$app_settings = fetchRow("SELECT * FROM app_settings WHERE id = 1");
$commission_rates = [
    'level1' => $app_settings['level1_rebate_percent'] ?? 20, // 20% for direct referrals
    'level2' => $app_settings['level2_rebate_percent'] ?? 10, // 10% for second level
    'level3' => $app_settings['level3_rebate_percent'] ?? 5   // 5% for third level
];

$page_title = "My Team";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bamboo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css" rel="stylesheet">
    <link href="team.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --primary-rgb: <?php echo implode(',', sscanf($primary_color, "#%02x%02x%02x")); ?>;
            --secondary-rgb: <?php echo implode(',', sscanf($secondary_color, "#%02x%02x%02x")); ?>;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-gradient-success">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/dashboard.php">
                <i class="bi bi-arrow-left me-2"></i>My Team
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bi bi-people-fill me-1"></i>
                    Team Size: <?php echo $team_stats['total_referrals']; ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Team Overview Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Team</h6>
                                <h3 class="mb-0"><?php echo $team_stats['total_referrals']; ?></h3>
                                <small>Active Members</small>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-people fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card bg-gradient-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Direct Referrals</h6>
                                <h3 class="mb-0"><?php echo $team_stats['direct_referrals']; ?></h3>
                                <small>Level 1 Team</small>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-person-plus fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card bg-gradient-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Commission</h6>
                                <h3 class="mb-0">$<?php echo number_format($team_stats['total_team_commission'], 2); ?></h3>
                                <small>All Time Earnings</small>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-currency-dollar fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card bg-gradient-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">This Month</h6>
                                <h3 class="mb-0">$<?php echo number_format($team_stats['this_month_commission'], 2); ?></h3>
                                <small>Commission Earned</small>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-calendar-month fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invitation Code Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card invitation-card">
                    <div class="card-header bg-gradient-primary text-white">
                        <h6 class="mb-0">
                            <i class="bi bi-share me-2"></i>
                            Your Invitation Code
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="invitation-code-display">
                                    <label class="form-label">Invitation Code</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-lg" id="invitationCode" 
                                               value="<?php echo htmlspecialchars($user['invitation_code']); ?>" readonly>
                                        <button class="btn btn-primary" type="button" onclick="copyInvitationCode()">
                                            <i class="bi bi-clipboard"></i> Copy
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="commission-rates">
                                    <h6>Commission Rates</h6>
                                    <div class="rate-item">
                                        <span class="level">Level 1 (Direct):</span>
                                        <span class="rate text-success fw-bold"><?php echo $commission_rates['level1']; ?>%</span>
                                    </div>
                                    <div class="rate-item">
                                        <span class="level">Level 2:</span>
                                        <span class="rate text-info fw-bold"><?php echo $commission_rates['level2']; ?>%</span>
                                    </div>
                                    <div class="rate-item">
                                        <span class="level">Level 3:</span>
                                        <span class="rate text-warning fw-bold"><?php echo $commission_rates['level3']; ?>%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Team Structure Visualization -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="bi bi-diagram-3 me-2"></i>
                            Team Structure
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="teamChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="bi bi-bar-chart me-2"></i>
                            Level Breakdown
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($level_stats as $level => $stats): ?>
                            <div class="level-stat-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="level-badge level-<?php echo $level; ?>">Level <?php echo $level; ?></span>
                                        <div class="level-details">
                                            <small class="text-muted"><?php echo $stats['count']; ?> members</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="commission-amount">$<?php echo number_format($stats['commission'], 2); ?></div>
                                        <small class="text-muted">Commission</small>
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar level-<?php echo $level; ?>" 
                                         style="width: <?php echo $stats['count'] > 0 ? min(100, ($stats['count'] / max(1, $team_stats['total_referrals'])) * 100) : 0; ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Direct Referrals Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="bi bi-people me-2"></i>
                            Direct Referrals (<?php echo count($direct_referrals); ?>)
                        </h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="exportTeamData()">
                                <i class="bi bi-download me-1"></i>Export
                            </button>
                            <button class="btn btn-primary" onclick="shareInvitationCode()">
                                <i class="bi bi-share me-1"></i>Share Code
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($direct_referrals)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-people display-1 text-muted"></i>
                                <h5 class="mt-3 text-muted">No Direct Referrals Yet</h5>
                                <p class="text-muted">Share your invitation code to start building your team!</p>
                                <button class="btn btn-primary" onclick="shareInvitationCode()">
                                    <i class="bi bi-share me-2"></i>Share Invitation Code
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Member</th>
                                            <th>VIP Level</th>
                                            <th>Balance</th>
                                            <th>Commission Generated</th>
                                            <th>Join Date</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($direct_referrals as $referral): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle">
                                                            <?php echo strtoupper(substr($referral['username'], 0, 2)); ?>
                                                        </div>
                                                        <div class="ms-2">
                                                            <div class="fw-bold"><?php echo htmlspecialchars($referral['username']); ?></div>
                                                            <small class="text-muted"><?php echo htmlspecialchars($referral['phone']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo htmlspecialchars($referral['vip_name']); ?></span>
                                                </td>
                                                <td>
                                                    <div class="fw-bold">$<?php echo number_format($referral['balance'], 2); ?></div>
                                                </td>
                                                <td>
                                                    <div class="fw-bold text-success">$<?php echo number_format($referral['total_commission_generated'], 2); ?></div>
                                                </td>
                                                <td>
                                                    <div><?php echo date('M j, Y', strtotime($referral['created_at'])); ?></div>
                                                    <small class="text-muted"><?php echo date('g:i A', strtotime($referral['created_at'])); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $referral['status'] === 'active' ? 'success' : 'warning'; ?>">
                                                        <?php echo ucfirst($referral['status']); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Team Building Tips -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card tips-card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="bi bi-lightbulb me-2"></i>
                            Team Building Tips
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>How to Earn More</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Share your invitation code with friends and family</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Help your referrals complete their daily tasks</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Encourage team members to upgrade their VIP levels</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Build a strong 3-level team structure</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Commission Structure</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-arrow-right text-primary me-2"></i>Level 1: <?php echo $commission_rates['level1']; ?>% of their task earnings</li>
                                    <li><i class="bi bi-arrow-right text-info me-2"></i>Level 2: <?php echo $commission_rates['level2']; ?>% of their task earnings</li>
                                    <li><i class="bi bi-arrow-right text-warning me-2"></i>Level 3: <?php echo $commission_rates['level3']; ?>% of their task earnings</li>
                                    <li><i class="bi bi-info-circle text-muted me-2"></i>Commissions are paid instantly when tasks are completed</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div class="modal fade" id="shareModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Share Invitation Code</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <div class="invitation-code-large">
                            <?php echo htmlspecialchars($user['invitation_code']); ?>
                        </div>
                    </div>
                    <div class="share-buttons">
                        <button class="btn btn-success w-100 mb-2" onclick="shareViaWhatsApp()">
                            <i class="bi bi-whatsapp me-2"></i>Share via WhatsApp
                        </button>
                        <button class="btn btn-primary w-100 mb-2" onclick="shareViaFacebook()">
                            <i class="bi bi-facebook me-2"></i>Share on Facebook
                        </button>
                        <button class="btn btn-info w-100 mb-2" onclick="shareViaTwitter()">
                            <i class="bi bi-twitter me-2"></i>Share on Twitter
                        </button>
                        <button class="btn btn-secondary w-100" onclick="copyShareText()">
                            <i class="bi bi-clipboard me-2"></i>Copy Share Text
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Pass PHP data to JavaScript
        window.teamData = {
            levelStats: <?php echo json_encode($level_stats); ?>,
            invitationCode: '<?php echo htmlspecialchars($user['invitation_code']); ?>',
            username: '<?php echo htmlspecialchars($user['username']); ?>'
        };
    </script>

<?php
// Set additional JS for footer
$additional_js = '
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="team.js"></script>
';
include '../includes/user_footer.php';
?>
