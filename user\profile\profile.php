<?php
/**
 * Bamboo Web Application - User Profile Management (Redesigned)
 * Company: Notepadsly
 * Version: 2.0 - Organized & Streamlined
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Get VIP level information
$vip_level = fetchRow("SELECT * FROM vip_levels WHERE level = ?", [$user['vip_level']]);

// Get app settings
$app_name = getAppSetting('app_name', 'Bamboo');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Check withdrawal PIN status
$has_withdrawal_pin = !empty($user['withdrawal_pin']);

// Handle POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $username = trim($_POST['username'] ?? '');
                $phone = trim($_POST['phone'] ?? '');
                $email = trim($_POST['email'] ?? '');
                $gender = $_POST['gender'] ?? '';
                
                if (empty($username)) {
                    showError('Username is required.');
                } elseif (empty($phone)) {
                    showError('Phone number is required.');
                } else {
                    // Check if username is already taken by another user
                    $existing_user = fetchRow("SELECT id FROM users WHERE username = ? AND id != ?", [$username, $user_id]);
                    if ($existing_user) {
                        showError('Username is already taken.');
                    } else {
                        $update_data = [
                            'username' => $username,
                            'phone' => $phone,
                            'email' => $email,
                            'gender' => $gender
                        ];
                        
                        if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                            $_SESSION['username'] = $username; // Update session
                            showSuccess('Profile updated successfully!');
                            // Refresh user data
                            $user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
                        } else {
                            showError('Failed to update profile. Please try again.');
                        }
                    }
                }
                break;
                
            case 'change_password':
                $current_password = $_POST['current_password'] ?? '';
                $new_password = $_POST['new_password'] ?? '';
                $confirm_password = $_POST['confirm_password'] ?? '';
                
                if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                    showError('All password fields are required.');
                } elseif ($new_password !== $confirm_password) {
                    showError('New passwords do not match.');
                } elseif (strlen($new_password) < 6) {
                    showError('New password must be at least 6 characters long.');
                } elseif (!password_verify($current_password, $user['password'])) {
                    showError('Current password is incorrect.');
                } else {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    if (updateRecord('users', ['password' => $hashed_password], 'id = ?', [$user_id])) {
                        showSuccess('Password changed successfully!');
                    } else {
                        showError('Failed to change password. Please try again.');
                    }
                }
                break;
                
            case 'set_withdrawal_pin':
                $new_pin = $_POST['new_pin'] ?? '';
                $confirm_pin = $_POST['confirm_pin'] ?? '';
                
                if (empty($new_pin) || empty($confirm_pin)) {
                    showError('Both PIN fields are required.');
                } elseif ($new_pin !== $confirm_pin) {
                    showError('PINs do not match.');
                } elseif (!preg_match('/^\d{4,6}$/', $new_pin)) {
                    showError('PIN must be 4-6 digits only.');
                } else {
                    $hashed_pin = password_hash($new_pin, PASSWORD_DEFAULT);
                    if (updateRecord('users', ['withdrawal_pin' => $hashed_pin], 'id = ?', [$user_id])) {
                        showSuccess('Withdrawal PIN set successfully!');
                        $has_withdrawal_pin = true;
                        // Refresh user data
                        $user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
                    } else {
                        showError('Failed to set PIN. Please try again.');
                    }
                }
                break;
                
            case 'update_wallet_info':
                $wallet_address = trim($_POST['wallet_address'] ?? '');
                $exchange_name = trim($_POST['exchange_name'] ?? '');
                
                if (empty($wallet_address)) {
                    showError('Wallet address is required.');
                } else {
                    $update_data = [
                        'wallet_address' => $wallet_address,
                        'exchange_name' => $exchange_name
                    ];
                    
                    if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                        showSuccess('Wallet information updated successfully!');
                        // Refresh user data
                        $user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
                    } else {
                        showError('Failed to update wallet information. Please try again.');
                    }
                }
                break;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Management - <?php echo htmlspecialchars($app_name); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo BASE_URL; ?>assets/css/user-dashboard.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
        }
        
        body {
            background-color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .profile-header {
            background: #ffffff;
            color: #333;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border: 4px solid rgba(255,255,255,0.3);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .profile-stats {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(10px);
        }
        
        .action-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .settings-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .quick-action-btn {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .status-badge {
            font-size: 0.85rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
        <div class="container-fluid px-4">
            <a class="navbar-brand d-flex align-items-center" href="<?php echo BASE_URL; ?>user/dashboard/dashboard.php">
                <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo htmlspecialchars($app_name); ?>" height="40" class="me-2">
                <span class="fw-bold"><?php echo htmlspecialchars($app_name); ?></span>
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="user-avatar me-2" style="width: 32px; height: 32px;">
                            <?php if ($user['avatar_url']): ?>
                                <img src="<?php echo BASE_URL . $user['avatar_url']; ?>" alt="Avatar" class="rounded-circle w-100 h-100" style="object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-light text-dark rounded-circle d-flex align-items-center justify-content-center w-100 h-100" style="font-size: 0.8rem;">
                                    <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="d-flex flex-column align-items-start">
                            <small class="mb-0"><?php echo htmlspecialchars($user['username']); ?></small>
                            <small class="opacity-75">Balance: USDT <?php echo number_format($user['balance'], 2); ?></small>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/dashboard/dashboard.php"><i class="bi bi-house me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/profile/profile.php"><i class="bi bi-person me-2"></i>Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/login/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid px-4 py-3">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-10">
                
                <!-- Profile Header Section -->
                <div class="profile-header">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <div class="profile-avatar rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="background: #f8f9fa; border: 2px solid #e9ecef;">
                                <?php if ($user['avatar_url']): ?>
                                    <img src="<?php echo BASE_URL . $user['avatar_url']; ?>" alt="Avatar" class="rounded-circle w-100 h-100" style="object-fit: cover;">
                                <?php else: ?>
                                    <span style="font-size: 3rem; font-weight: bold; color: var(--primary-color);">
                                        <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="badge bg-light text-dark fs-6 px-3 py-2">VIP Level <?php echo $user['vip_level']; ?></div>
                        </div>
                        <div class="col-md-6">
                            <h2 class="mb-3"><?php echo htmlspecialchars($user['username']); ?></h2>
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-2">
                                        <small class="opacity-75">Current Balance</small>
                                        <div class="h4 mb-0">USDT <?php echo number_format($user['balance'], 2); ?></div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-2">
                                        <small class="opacity-75">Member Since</small>
                                        <div class="h6 mb-0"><?php echo date('M Y', strtotime($user['created_at'])); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="profile-stats">
                                <div class="text-center mb-2">
                                    <small class="opacity-75">Referral Code</small>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <code class="bg-light text-dark px-2 py-1 rounded me-2"><?php echo htmlspecialchars($user['invitation_code']); ?></code>
                                        <button class="btn btn-sm btn-light" onclick="copyReferralCode()" title="Copy">
                                            <i class="bi bi-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <small class="opacity-75">Credit Score</small>
                                    <div class="h5 mb-0"><?php echo $user['credit_score'] ?? 100; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                <!-- Account Status & Quick Actions -->
                <div class="row mb-4">
                    <!-- Account Status Card -->
                    <div class="col-lg-4 mb-4">
                        <div class="card action-card h-100">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="bi bi-shield-check me-2 text-success"></i>Account Status
                                </h5>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Account Verified:</span>
                                    <span class="status-badge bg-success text-white">
                                        <i class="bi bi-check-circle me-1"></i>Verified
                                    </span>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Withdrawal PIN:</span>
                                    <?php if ($has_withdrawal_pin): ?>
                                        <span class="status-badge bg-success text-white">
                                            <i class="bi bi-lock me-1"></i>Set
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge bg-warning text-dark">
                                            <i class="bi bi-unlock me-1"></i>Not Set
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Wallet Address:</span>
                                    <?php if (!empty($user['wallet_address'])): ?>
                                        <span class="status-badge bg-success text-white">
                                            <i class="bi bi-wallet2 me-1"></i>Set
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge bg-warning text-dark">
                                            <i class="bi bi-wallet2 me-1"></i>Not Set
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Actions Card -->
                    <div class="col-lg-4 mb-4">
                        <div class="card action-card h-100">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="bi bi-wallet me-2 text-primary"></i>Financial Actions
                                </h5>

                                <div class="d-grid gap-2">
                                    <a href="<?php echo BASE_URL; ?>user/deposit/deposit.php" class="btn btn-primary quick-action-btn">
                                        <i class="bi bi-plus-circle me-2"></i>Deposit Funds
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>user/withdraw/withdraw.php" class="btn btn-success quick-action-btn">
                                        <i class="bi bi-arrow-up-circle me-2"></i>Withdraw Funds
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>user/transactions/transactions.php" class="btn btn-info quick-action-btn">
                                        <i class="bi bi-list-ul me-2"></i>View Transactions
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>user/vip/vip.php" class="btn btn-warning quick-action-btn">
                                        <i class="bi bi-star me-2"></i>VIP Levels
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Platform Actions Card -->
                    <div class="col-lg-4 mb-4">
                        <div class="card action-card h-100">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="bi bi-grid me-2 text-secondary"></i>Platform Actions
                                </h5>

                                <div class="d-grid gap-2">
                                    <a href="<?php echo BASE_URL; ?>user/tasks/tasks.php" class="btn btn-outline-primary quick-action-btn">
                                        <i class="bi bi-check-square me-2"></i>Complete Tasks
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>user/team/team.php" class="btn btn-outline-success quick-action-btn">
                                        <i class="bi bi-people me-2"></i>My Team
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>user/certificate/certificate.php" class="btn btn-outline-info quick-action-btn">
                                        <i class="bi bi-award me-2"></i>Certificate
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>user/dashboard/dashboard.php" class="btn btn-outline-secondary quick-action-btn">
                                        <i class="bi bi-house me-2"></i>Dashboard
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings & Information Section -->
                <div class="row">
                    <!-- Personal Information Settings -->
                    <div class="col-lg-6 mb-4">
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="bi bi-person-gear me-2 text-primary"></i>Personal Information
                            </h5>

                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_profile">

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" name="username"
                                               value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="text" class="form-control" id="phone" name="phone"
                                               value="<?php echo htmlspecialchars($user['phone']); ?>" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email (Optional)</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="gender" class="form-label">Gender</label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="Male" <?php echo ($user['gender'] === 'Male') ? 'selected' : ''; ?>>Male</option>
                                            <option value="Female" <?php echo ($user['gender'] === 'Female') ? 'selected' : ''; ?>>Female</option>
                                        </select>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>Update Profile
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="col-lg-6 mb-4">
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="bi bi-shield-lock me-2 text-danger"></i>Security Settings
                            </h5>

                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary quick-action-btn" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                    <i class="bi bi-key me-2"></i>Change Password
                                </button>

                                <button type="button" class="btn btn-outline-warning quick-action-btn" data-bs-toggle="modal" data-bs-target="#setPinModal">
                                    <i class="bi bi-shield me-2"></i><?php echo $has_withdrawal_pin ? 'Update PIN' : 'Set Withdrawal PIN'; ?>
                                </button>

                                <button type="button" class="btn btn-outline-info quick-action-btn" data-bs-toggle="modal" data-bs-target="#updateWalletModal">
                                    <i class="bi bi-wallet2 me-2"></i>Update Wallet Info
                                </button>

                                <a href="<?php echo BASE_URL; ?>user/login/logout.php" class="btn btn-outline-danger quick-action-btn">
                                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                                </a>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="settings-section mt-3">
                            <h6 class="mb-3 text-muted">Quick Statistics</h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="h6 text-primary mb-0">USDT <?php echo number_format($user['total_deposited'] ?? 0, 2); ?></div>
                                    <small class="text-muted">Deposited</small>
                                </div>
                                <div class="col-4">
                                    <div class="h6 text-success mb-0">USDT <?php echo number_format($user['total_withdrawn'] ?? 0, 2); ?></div>
                                    <small class="text-muted">Withdrawn</small>
                                </div>
                                <div class="col-4">
                                    <div class="h6 text-info mb-0"><?php echo $user['total_referrals'] ?? 0; ?></div>
                                    <small class="text-muted">Referrals</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Modal Forms -->
    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="change_password">

                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Set/Update PIN Modal -->
    <div class="modal fade" id="setPinModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo $has_withdrawal_pin ? 'Update PIN' : 'Set Withdrawal PIN'; ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="set_withdrawal_pin">

                        <div class="mb-3">
                            <label for="new_pin" class="form-label">New PIN (4-6 digits)</label>
                            <input type="password" class="form-control" id="new_pin" name="new_pin" required pattern="\d{4,6}" maxlength="6">
                        </div>

                        <div class="mb-3">
                            <label for="confirm_pin" class="form-label">Confirm PIN</label>
                            <input type="password" class="form-control" id="confirm_pin" name="confirm_pin" required pattern="\d{4,6}" maxlength="6">
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            Your withdrawal PIN is required for all withdrawal requests and must be 4-6 digits only.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning"><?php echo $has_withdrawal_pin ? 'Update PIN' : 'Set PIN'; ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Update Wallet Modal -->
    <div class="modal fade" id="updateWalletModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Wallet Information</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="update_wallet_info">

                        <div class="mb-3">
                            <label for="wallet_address" class="form-label">Wallet Address</label>
                            <input type="text" class="form-control" id="wallet_address" name="wallet_address"
                                   value="<?php echo htmlspecialchars($user['wallet_address'] ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="exchange_name" class="form-label">Exchange Name (Optional)</label>
                            <input type="text" class="form-control" id="exchange_name" name="exchange_name"
                                   value="<?php echo htmlspecialchars($user['exchange_name'] ?? ''); ?>"
                                   placeholder="e.g., Binance, Coinbase, etc.">
                        </div>

                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Please ensure your wallet address is correct. Incorrect addresses may result in loss of funds.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-info">Update Wallet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Information Modal -->
    <div class="modal fade" id="infoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="infoModalTitle">Information</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="infoModalBody">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Navigation -->
    <footer class="bg-white border-top mt-5">
        <div class="container-fluid">
            <div class="row py-3">
                <div class="col-3 text-center">
                    <a href="<?php echo BASE_URL; ?>user/dashboard/dashboard.php" class="text-decoration-none text-muted">
                        <i class="bi bi-house-door fs-4 d-block"></i>
                        <small>Home</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="<?php echo BASE_URL; ?>user/tasks/tasks.php" class="text-decoration-none text-muted">
                        <i class="bi bi-check-square fs-4 d-block"></i>
                        <small>Submission</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="<?php echo BASE_URL; ?>user/transactions/transactions.php" class="text-decoration-none text-muted">
                        <i class="bi bi-list-ul fs-4 d-block"></i>
                        <small>Records</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="<?php echo BASE_URL; ?>user/profile/profile.php" class="text-decoration-none" style="color: var(--primary-color);">
                        <i class="bi bi-person-circle fs-4 d-block"></i>
                        <small>Profile</small>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Copy referral code function
        function copyReferralCode() {
            const referralCode = '<?php echo htmlspecialchars($user['invitation_code']); ?>';
            navigator.clipboard.writeText(referralCode).then(function() {
                // Show success message
                const btn = event.target.closest('button');
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="bi bi-check"></i>';
                btn.classList.add('btn-success');
                btn.classList.remove('btn-light', 'btn-outline-primary');

                setTimeout(function() {
                    btn.innerHTML = originalHTML;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-light');
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = referralCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                // Show success message
                const btn = event.target.closest('button');
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="bi bi-check"></i>';
                btn.classList.add('btn-success');
                btn.classList.remove('btn-light', 'btn-outline-primary');

                setTimeout(function() {
                    btn.innerHTML = originalHTML;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-light');
                }, 2000);
            });
        }

        // Show information modal
        function showModal(type) {
            const modal = new bootstrap.Modal(document.getElementById('infoModal'));
            const titleElement = document.getElementById('infoModalTitle');
            const bodyElement = document.getElementById('infoModalBody');

            // Set loading state
            bodyElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

            // Set title based on type
            switch(type) {
                case 'terms':
                    titleElement.textContent = 'Terms & Conditions';
                    break;
                case 'faq':
                    titleElement.textContent = 'Frequently Asked Questions';
                    break;
                case 'campaign':
                    titleElement.textContent = 'Latest Campaign';
                    break;
            }

            // Show modal
            modal.show();

            // Load content via AJAX
            fetch('<?php echo BASE_URL; ?>api/get_modal_content.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: type,
                    csrf_token: '<?php echo generateCSRFToken(); ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    bodyElement.innerHTML = data.content;
                } else {
                    bodyElement.innerHTML = '<div class="alert alert-warning">Content not available at the moment.</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                bodyElement.innerHTML = '<div class="alert alert-danger">Error loading content.</div>';
            });
        }

        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            // Password confirmation validation
            const newPasswordField = document.getElementById('new_password');
            const confirmPasswordField = document.getElementById('confirm_password');

            if (newPasswordField && confirmPasswordField) {
                function validatePasswords() {
                    if (newPasswordField.value !== confirmPasswordField.value) {
                        confirmPasswordField.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPasswordField.setCustomValidity('');
                    }
                }

                newPasswordField.addEventListener('input', validatePasswords);
                confirmPasswordField.addEventListener('input', validatePasswords);
            }

            // PIN confirmation validation
            const newPinField = document.getElementById('new_pin');
            const confirmPinField = document.getElementById('confirm_pin');

            if (newPinField && confirmPinField) {
                function validatePins() {
                    if (newPinField.value !== confirmPinField.value) {
                        confirmPinField.setCustomValidity('PINs do not match');
                    } else {
                        confirmPinField.setCustomValidity('');
                    }
                }

                newPinField.addEventListener('input', validatePins);
                confirmPinField.addEventListener('input', validatePins);
            }
        });
    </script>

<?php
// Set additional JS for footer
$additional_js = '
    <script>
        // Profile page specific JavaScript can go here
    </script>
';
include '../includes/user_footer.php';
?>
