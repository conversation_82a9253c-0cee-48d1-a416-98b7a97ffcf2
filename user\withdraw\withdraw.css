/* Bamboo Withdraw Page Styles */

:root {
    --primary-color: #28a745;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: translateX(-5px);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: 1px solid rgba(0,0,0,0.125);
}

/* Form Styles */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-check-input:checked {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.form-check-label {
    cursor: pointer;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: block;
    width: 100%;
}

.form-check-input:checked + .form-check-label {
    border-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.1);
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1aa179 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-left: 4px solid var(--info-color);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid var(--warning-color);
}

/* Fee Calculation Card */
.card.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 1px solid #dee2e6;
}

.text-sm {
    font-size: 0.875rem;
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 50px;
}

.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

/* Requirements Check Icons */
.bi-check-circle {
    color: var(--success-color) !important;
}

.bi-x-circle {
    color: var(--danger-color) !important;
}

/* Withdrawal History */
.border-bottom:last-child {
    border-bottom: none !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading States */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* PIN Input Styling */
#withdrawal_pin {
    font-family: 'Courier New', monospace;
    font-size: 1.2em;
    text-align: center;
    letter-spacing: 0.5em;
}

/* USDT Address Input */
#usdt_address {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

/* Amount Input Highlighting */
#amount:focus {
    background-color: rgba(40, 167, 69, 0.05);
}

/* Fee Calculation Highlighting */
#withdrawalAmount,
#feeAmount,
#netAmount {
    font-size: 1.1em;
    font-weight: 600;
}

#feeAmount {
    color: var(--danger-color) !important;
}

#netAmount {
    color: var(--success-color) !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .navbar-text {
        font-size: 0.9em;
    }
    
    .form-check-label {
        padding: 8px;
        font-size: 0.9em;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .text-sm {
        font-size: 0.8rem;
    }
    
    .row.text-sm .col-4 {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-header h5,
    .card-header h6 {
        font-size: 1rem;
    }
    
    .alert {
        padding: 0.75rem;
    }
    
    .alert .row .col-md-6 {
        margin-bottom: 0.5rem;
    }
    
    #withdrawal_pin {
        font-size: 1em;
        letter-spacing: 0.3em;
    }
    
    .card.bg-light .row .col-4 {
        text-align: center;
        margin-bottom: 0.75rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .card-header.bg-light {
        background-color: #3d3d3d !important;
        color: #ffffff;
    }
    
    .card.bg-light {
        background: linear-gradient(135deg, #3d3d3d 0%, #2d2d2d 100%) !important;
        color: #ffffff;
    }
    
    .form-control {
        background-color: #3d3d3d;
        border-color: #555;
        color: #ffffff;
    }
    
    .form-control:focus {
        background-color: #3d3d3d;
        border-color: var(--success-color);
        color: #ffffff;
    }
    
    .form-check-label {
        background-color: #2d2d2d;
        color: #ffffff;
        border-color: #555;
    }
    
    .form-check-input:checked + .form-check-label {
        background-color: rgba(40, 167, 69, 0.2);
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .alert-dismissible .btn-close {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background-color: white !important;
    }
}
