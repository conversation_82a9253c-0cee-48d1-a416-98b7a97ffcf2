<?php
/**
 * Bamboo Web Application - User Transactions Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Set page title for header
$page_title = 'Transaction History';

// Get filter parameters
$filter_type = $_GET['type'] ?? 'all';
$filter_status = $_GET['status'] ?? 'all';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build WHERE clause for filters
$where_conditions = ["user_id = ?"];
$params = [$user_id];

if ($filter_type !== 'all') {
    $where_conditions[] = "type = ?";
    $params[] = $filter_type;
}

if ($filter_status !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "DATE(created_at) >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "DATE(created_at) <= ?";
    $params[] = $filter_date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
$total_transactions = getRecordCount('transactions', $where_clause, $params);
$total_pages = ceil($total_transactions / $per_page);

// Get transactions with pagination
$transactions = fetchAll(
    "SELECT * FROM transactions WHERE $where_clause ORDER BY created_at DESC LIMIT ? OFFSET ?",
    array_merge($params, [$per_page, $offset])
);

// Get transaction statistics
$stats = [
    'total_deposits' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'deposit' AND status = 'completed'", [$user_id]),
    'total_withdrawals' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'withdrawal' AND status = 'completed'", [$user_id]),
    'pending_deposits' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'deposit' AND status = 'pending'", [$user_id]),
    'pending_withdrawals' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'withdrawal' AND status = 'pending'", [$user_id]),
];

// Get app settings for theme colors
$app_name = getAppSetting('app_name', 'Bamboo');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Additional CSS for this page
$additional_css = '
    <style>
        .transaction-card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .transaction-type-deposit {
            border-left: 4px solid #28a745;
        }

        .transaction-type-withdrawal {
            border-left: 4px solid #dc3545;
        }

        .transaction-type-commission {
            border-left: 4px solid #007bff;
        }

        .transaction-type-task {
            border-left: 4px solid #ffc107;
        }

        .transaction-amount-positive {
            color: #28a745;
            font-weight: bold;
        }

        .transaction-amount-negative {
            color: #dc3545;
            font-weight: bold;
        }

        .transaction-status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .transaction-status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .transaction-status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
';

// Include standardized user header
include '../includes/user_header.php';
?>

    <div class="container mt-4">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Deposits</h6>
                                <h4 class="mb-0">$<?php echo number_format($stats['total_deposits'], 2); ?></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-arrow-down-circle fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Withdrawals</h6>
                                <h4 class="mb-0">$<?php echo number_format($stats['total_withdrawals'], 2); ?></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-arrow-up-circle fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Pending Deposits</h6>
                                <h4 class="mb-0">$<?php echo number_format($stats['pending_deposits'], 2); ?></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-clock fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Pending Withdrawals</h6>
                                <h4 class="mb-0">$<?php echo number_format($stats['pending_withdrawals'], 2); ?></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-hourglass-split fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0"><i class="bi bi-funnel me-2"></i>Filter Transactions</h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="type" class="form-label">Transaction Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>All Types</option>
                            <option value="deposit" <?php echo $filter_type === 'deposit' ? 'selected' : ''; ?>>Deposits</option>
                            <option value="withdrawal" <?php echo $filter_type === 'withdrawal' ? 'selected' : ''; ?>>Withdrawals</option>
                            <option value="commission" <?php echo $filter_type === 'commission' ? 'selected' : ''; ?>>Commissions</option>
                            <option value="bonus" <?php echo $filter_type === 'bonus' ? 'selected' : ''; ?>>Bonuses</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="all" <?php echo $filter_status === 'all' ? 'selected' : ''; ?>>All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search me-1"></i>Filter
                            </button>
                        </div>
                    </div>
                </form>
                <?php if ($filter_type !== 'all' || $filter_status !== 'all' || !empty($filter_date_from) || !empty($filter_date_to)): ?>
                    <div class="mt-3">
                        <a href="transactions.php" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-x-circle me-1"></i>Clear Filters
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="bi bi-list-ul me-2"></i>Transactions (<?php echo $total_transactions; ?> total)</h6>
                <div class="btn-group btn-group-sm" role="group">
                    <a href="../deposit/deposit.php" class="btn btn-success">
                        <i class="bi bi-plus-circle me-1"></i>Deposit
                    </a>
                    <a href="../withdraw/withdraw.php" class="btn btn-primary">
                        <i class="bi bi-cash-coin me-1"></i>Withdraw
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($transactions)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">No transactions found</h5>
                        <p class="text-muted">
                            <?php if ($filter_type !== 'all' || $filter_status !== 'all' || !empty($filter_date_from) || !empty($filter_date_to)): ?>
                                Try adjusting your filters or <a href="transactions.php">view all transactions</a>
                            <?php else: ?>
                                Start by making your first <a href="../deposit/deposit.php">deposit</a>
                            <?php endif; ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                            <small class="text-muted"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo getTransactionTypeBadgeClass($transaction['type']); ?>">
                                                <i class="bi bi-<?php echo getTransactionTypeIcon($transaction['type']); ?> me-1"></i>
                                                <?php echo ucfirst($transaction['type']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="fw-bold <?php echo $transaction['type'] === 'deposit' || $transaction['type'] === 'commission' || $transaction['type'] === 'bonus' ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo $transaction['type'] === 'deposit' || $transaction['type'] === 'commission' || $transaction['type'] === 'bonus' ? '+' : '-'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                            </div>
                                            <?php if ($transaction['fee_amount'] > 0): ?>
                                                <small class="text-muted">Fee: $<?php echo number_format($transaction['fee_amount'], 2); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo getTransactionStatusBadgeClass($transaction['status']); ?>">
                                                <?php echo ucfirst($transaction['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div><?php echo htmlspecialchars($transaction['description']); ?></div>
                                            <?php if ($transaction['admin_notes']): ?>
                                                <small class="text-muted">Note: <?php echo htmlspecialchars($transaction['admin_notes']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewTransactionDetails(<?php echo $transaction['id']; ?>)">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="card-footer">
                            <nav aria-label="Transaction pagination">
                                <ul class="pagination pagination-sm justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                                <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    $start_page = max(1, $page - 2);
                                    $end_page = min($total_pages, $page + 2);
                                    
                                    for ($i = $start_page; $i <= $end_page; $i++):
                                    ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                                <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Transaction Details Modal -->
    <div class="modal fade" id="transactionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Transaction Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="transactionModalBody">
                    <!-- Content loaded via JavaScript -->
                </div>
            </div>
        </div>
    </div>

<?php
// Set additional JS for footer
$additional_js = '
    <script src="transactions.js"></script>
';
include '../includes/user_footer.php';
?>

<?php
// Helper functions for transaction display
function getTransactionTypeBadgeClass($type) {
    switch ($type) {
        case 'deposit': return 'success';
        case 'withdrawal': return 'primary';
        case 'commission': return 'info';
        case 'bonus': return 'warning';
        default: return 'secondary';
    }
}

function getTransactionTypeIcon($type) {
    switch ($type) {
        case 'deposit': return 'arrow-down-circle';
        case 'withdrawal': return 'arrow-up-circle';
        case 'commission': return 'star';
        case 'bonus': return 'gift';
        default: return 'circle';
    }
}

function getTransactionStatusBadgeClass($status) {
    switch ($status) {
        case 'completed': return 'success';
        case 'pending': return 'warning';
        case 'failed': return 'danger';
        case 'cancelled': return 'secondary';
        default: return 'secondary';
    }
}

// Set additional JS for footer
$additional_js = '
    <script src="transactions.js"></script>
';
include '../includes/user_footer.php';
?>
