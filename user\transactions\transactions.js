/**
 * Bamboo Transactions Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize transactions page functionality
    initializeTransactionsPage();
});

function initializeTransactionsPage() {
    // Setup date range validation
    setupDateRangeValidation();
    
    // Setup table enhancements
    setupTableEnhancements();
    
    // Setup modal functionality
    setupModalFunctionality();
    
    // Setup responsive table
    setupResponsiveTable();
    
    // Setup auto-refresh
    setupAutoRefresh();
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
}

function setupDateRangeValidation() {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    if (!dateFromInput || !dateToInput) return;
    
    // Validate date range
    function validateDateRange() {
        const fromDate = new Date(dateFromInput.value);
        const toDate = new Date(dateToInput.value);
        
        if (dateFromInput.value && dateToInput.value) {
            if (fromDate > toDate) {
                dateToInput.setCustomValidity('End date must be after start date');
                showToast('End date must be after start date', 'error');
            } else {
                dateToInput.setCustomValidity('');
            }
        }
        
        // Check if date range is too large (more than 1 year)
        if (dateFromInput.value && dateToInput.value) {
            const diffTime = Math.abs(toDate - fromDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays > 365) {
                showToast('Date range cannot exceed 1 year', 'warning');
            }
        }
    }
    
    dateFromInput.addEventListener('change', validateDateRange);
    dateToInput.addEventListener('change', validateDateRange);
    
    // Set max date to today
    const today = new Date().toISOString().split('T')[0];
    dateFromInput.setAttribute('max', today);
    dateToInput.setAttribute('max', today);
}

function setupTableEnhancements() {
    // Add hover effects and click handlers
    const tableRows = document.querySelectorAll('.table tbody tr');
    
    tableRows.forEach(row => {
        // Add click handler for row selection
        row.addEventListener('click', function(e) {
            // Don't trigger if clicking on a button
            if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                return;
            }
            
            // Remove previous selection
            tableRows.forEach(r => r.classList.remove('table-active'));
            
            // Add selection to current row
            this.classList.add('table-active');
        });
        
        // Add double-click handler to view details
        row.addEventListener('dblclick', function() {
            const viewButton = this.querySelector('button[onclick*="viewTransactionDetails"]');
            if (viewButton) {
                viewButton.click();
            }
        });
    });
    
    // Add sorting functionality to table headers
    setupTableSorting();
}

function setupTableSorting() {
    const headers = document.querySelectorAll('.table thead th');
    
    headers.forEach((header, index) => {
        // Skip action column
        if (index === headers.length - 1) return;
        
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(index, this);
        });
        
        // Add sort indicator
        const sortIcon = document.createElement('i');
        sortIcon.className = 'bi bi-arrow-down-up ms-1 text-muted';
        sortIcon.style.fontSize = '0.8em';
        header.appendChild(sortIcon);
    });
}

function sortTable(columnIndex, headerElement) {
    const table = document.querySelector('.table tbody');
    const rows = Array.from(table.querySelectorAll('tr'));
    const isAscending = !headerElement.classList.contains('sort-asc');
    
    // Remove sort classes from all headers
    document.querySelectorAll('.table thead th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('i');
        if (icon) {
            icon.className = 'bi bi-arrow-down-up ms-1 text-muted';
        }
    });
    
    // Add sort class to current header
    headerElement.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    const icon = headerElement.querySelector('i');
    if (icon) {
        icon.className = `bi bi-${isAscending ? 'sort-alpha-down' : 'sort-alpha-up'} ms-1`;
    }
    
    // Sort rows
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        // Handle different data types
        let comparison = 0;
        if (columnIndex === 0) { // Date column
            comparison = new Date(aValue) - new Date(bValue);
        } else if (columnIndex === 2) { // Amount column
            const aAmount = parseFloat(aValue.replace(/[^0-9.-]/g, ''));
            const bAmount = parseFloat(bValue.replace(/[^0-9.-]/g, ''));
            comparison = aAmount - bAmount;
        } else {
            comparison = aValue.localeCompare(bValue);
        }
        
        return isAscending ? comparison : -comparison;
    });
    
    // Reorder table rows
    rows.forEach(row => table.appendChild(row));
    
    showToast(`Table sorted by ${headerElement.textContent.trim()}`, 'info');
}

function setupModalFunctionality() {
    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('transactionModal'));
    window.transactionModal = modal;
}

function viewTransactionDetails(transactionId) {
    const modalBody = document.getElementById('transactionModalBody');
    
    // Show loading state
    modalBody.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading transaction details...</p>
        </div>
    `;
    
    // Show modal
    window.transactionModal.show();
    
    // Fetch transaction details
    fetch(`get_transaction_details.php?id=${transactionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTransactionDetails(data.transaction);
            } else {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Error loading transaction details: ${data.message || 'Unknown error'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error fetching transaction details:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Error loading transaction details. Please try again.
                </div>
            `;
        });
}

function displayTransactionDetails(transaction) {
    const modalBody = document.getElementById('transactionModalBody');
    
    const paymentDetails = transaction.payment_method_details ? 
        JSON.parse(transaction.payment_method_details) : null;
    
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Transaction Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Transaction ID:</strong></td>
                        <td>#${transaction.id}</td>
                    </tr>
                    <tr>
                        <td><strong>Type:</strong></td>
                        <td>
                            <span class="badge bg-${getTransactionTypeBadgeClass(transaction.type)}">
                                ${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge bg-${getTransactionStatusBadgeClass(transaction.status)}">
                                ${transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Amount:</strong></td>
                        <td class="fw-bold ${transaction.type === 'deposit' || transaction.type === 'commission' ? 'text-success' : 'text-danger'}">
                            ${transaction.type === 'deposit' || transaction.type === 'commission' ? '+' : '-'}$${parseFloat(transaction.amount).toFixed(2)}
                        </td>
                    </tr>
                    ${transaction.fee_amount > 0 ? `
                    <tr>
                        <td><strong>Fee:</strong></td>
                        <td class="text-danger">$${parseFloat(transaction.fee_amount).toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td><strong>Net Amount:</strong></td>
                        <td class="fw-bold">$${parseFloat(transaction.net_amount || transaction.amount).toFixed(2)}</td>
                    </tr>
                    ` : ''}
                    <tr>
                        <td><strong>Date:</strong></td>
                        <td>${new Date(transaction.created_at).toLocaleString()}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Additional Details</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Description:</strong></td>
                        <td>${transaction.description || 'N/A'}</td>
                    </tr>
                    ${paymentDetails ? `
                    <tr>
                        <td><strong>Network:</strong></td>
                        <td>${paymentDetails.network || 'N/A'}</td>
                    </tr>
                    ${paymentDetails.transaction_hash ? `
                    <tr>
                        <td><strong>Transaction Hash:</strong></td>
                        <td class="text-break" style="font-family: monospace; font-size: 0.8em;">
                            ${paymentDetails.transaction_hash}
                        </td>
                    </tr>
                    ` : ''}
                    ${paymentDetails.usdt_address ? `
                    <tr>
                        <td><strong>USDT Address:</strong></td>
                        <td class="text-break" style="font-family: monospace; font-size: 0.8em;">
                            ${paymentDetails.usdt_address}
                        </td>
                    </tr>
                    ` : ''}
                    ` : ''}
                    ${transaction.admin_notes ? `
                    <tr>
                        <td><strong>Admin Notes:</strong></td>
                        <td>${transaction.admin_notes}</td>
                    </tr>
                    ` : ''}
                    ${transaction.processed_at ? `
                    <tr>
                        <td><strong>Processed:</strong></td>
                        <td>${new Date(transaction.processed_at).toLocaleString()}</td>
                    </tr>
                    ` : ''}
                </table>
            </div>
        </div>
        
        ${transaction.screenshot_path ? `
        <div class="mt-3">
            <h6>Screenshot</h6>
            <img src="../../${transaction.screenshot_path}" class="img-fluid rounded" alt="Transaction Screenshot" style="max-height: 300px;">
        </div>
        ` : ''}
    `;
}

function setupResponsiveTable() {
    // Add data labels for mobile view
    const table = document.querySelector('.table');
    if (!table) return;
    
    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
    
    table.querySelectorAll('tbody tr').forEach(row => {
        Array.from(row.cells).forEach((cell, index) => {
            if (headers[index]) {
                cell.setAttribute('data-label', headers[index]);
            }
        });
    });
}

function setupAutoRefresh() {
    // Auto-refresh every 5 minutes if there are pending transactions
    const hasPendingTransactions = document.querySelector('.badge.bg-warning');
    
    if (hasPendingTransactions) {
        setInterval(() => {
            // Only refresh if user is still on the page and hasn't interacted recently
            if (document.visibilityState === 'visible' && 
                Date.now() - window.lastUserInteraction > 60000) {
                location.reload();
            }
        }, 300000); // 5 minutes
    }
    
    // Track user interaction
    window.lastUserInteraction = Date.now();
    ['click', 'scroll', 'keypress'].forEach(event => {
        document.addEventListener(event, () => {
            window.lastUserInteraction = Date.now();
        });
    });
}

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + F for filter focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const typeSelect = document.getElementById('type');
            if (typeSelect) {
                typeSelect.focus();
            }
        }
        
        // Escape to close modal
        if (e.key === 'Escape' && window.transactionModal) {
            window.transactionModal.hide();
        }
    });
}

// Helper functions
function getTransactionTypeBadgeClass(type) {
    const classes = {
        'deposit': 'success',
        'withdrawal': 'primary',
        'commission': 'info',
        'bonus': 'warning'
    };
    return classes[type] || 'secondary';
}

function getTransactionStatusBadgeClass(status) {
    const classes = {
        'completed': 'success',
        'pending': 'warning',
        'failed': 'danger',
        'cancelled': 'secondary'
    };
    return classes[status] || 'secondary';
}

function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 8000 : 4000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Export functions for global access
window.viewTransactionDetails = viewTransactionDetails;
window.showToast = showToast;
