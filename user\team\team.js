/**
 * Bamboo Team Management Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize team page functionality
    initializeTeamPage();
});

function initializeTeamPage() {
    // Setup animations
    setupAnimations();
    
    // Setup team chart
    setupTeamChart();
    
    // Setup tooltips
    setupTooltips();
    
    // Setup modal functionality
    setupModalFunctionality();
    
    // Setup auto-refresh
    setupAutoRefresh();
}

function setupAnimations() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
    
    // Add scale-in animation to stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.05}s`;
        card.classList.add('scale-in');
    });
    
    // Animate progress bars
    setTimeout(() => {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });
    }, 500);
}

function setupTeamChart() {
    const ctx = document.getElementById('teamChart');
    if (!ctx || !window.teamData) return;
    
    const levelStats = window.teamData.levelStats;
    const labels = ['Level 1', 'Level 2', 'Level 3'];
    const memberCounts = [
        levelStats[1].count,
        levelStats[2].count,
        levelStats[3].count
    ];
    const commissionData = [
        levelStats[1].commission,
        levelStats[2].commission,
        levelStats[3].commission
    ];
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Team Members',
                data: memberCounts,
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(23, 162, 184, 0.8)',
                    'rgba(255, 193, 7, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(23, 162, 184, 1)',
                    'rgba(255, 193, 7, 1)'
                ],
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }, {
                label: 'Commission ($)',
                data: commissionData,
                backgroundColor: [
                    'rgba(40, 167, 69, 0.4)',
                    'rgba(23, 162, 184, 0.4)',
                    'rgba(255, 193, 7, 0.4)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(23, 162, 184, 0.8)',
                    'rgba(255, 193, 7, 0.8)'
                ],
                borderWidth: 1,
                borderRadius: 8,
                borderSkipped: false,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return `Members: ${context.parsed.y}`;
                            } else {
                                return `Commission: $${context.parsed.y.toFixed(2)}`;
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            weight: 'bold'
                        }
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Team Members'
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Commission ($)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

function setupTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupModalFunctionality() {
    // Initialize share modal
    const shareModal = new bootstrap.Modal(document.getElementById('shareModal'));
    window.shareModal = shareModal;
    
    // Setup modal event listeners
    const shareModalElement = document.getElementById('shareModal');
    shareModalElement.addEventListener('show.bs.modal', function() {
        // Add entrance animation
        this.querySelector('.modal-dialog').style.animation = 'slideInDown 0.3s ease-out';
    });
}

function setupAutoRefresh() {
    // Auto-refresh team stats every 5 minutes
    setInterval(() => {
        if (document.visibilityState === 'visible') {
            refreshTeamStats();
        }
    }, 300000); // 5 minutes
}

function copyInvitationCode() {
    const codeInput = document.getElementById('invitationCode');
    codeInput.select();
    codeInput.setSelectionRange(0, 99999); // For mobile devices
    
    navigator.clipboard.writeText(codeInput.value).then(() => {
        showToast('Invitation code copied to clipboard!', 'success');
        
        // Add visual feedback
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i> Copied!';
        button.classList.add('btn-success');
        button.classList.remove('btn-primary');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-primary');
        }, 2000);
    }).catch(() => {
        // Fallback for older browsers
        document.execCommand('copy');
        showToast('Invitation code copied!', 'success');
    });
}

function shareInvitationCode() {
    window.shareModal.show();
}

function shareViaWhatsApp() {
    const code = window.teamData.invitationCode;
    const username = window.teamData.username;
    const message = `🎉 Join me on Bamboo and start earning money by completing simple tasks!\n\n💰 Use my invitation code: ${code}\n\n✅ Easy daily tasks\n✅ Instant commissions\n✅ VIP benefits\n✅ Team bonuses\n\nInvited by: ${username}\n\nDownload now and start earning!`;
    
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareViaFacebook() {
    const shareUrl = window.location.origin;
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent('Join me on Bamboo and start earning! Use code: ' + window.teamData.invitationCode)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
}

function shareViaTwitter() {
    const code = window.teamData.invitationCode;
    const message = `🎉 Join me on Bamboo and start earning! Use invitation code: ${code} 💰 #BambooEarning #TaskEarning`;
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
}

function copyShareText() {
    const code = window.teamData.invitationCode;
    const username = window.teamData.username;
    const shareText = `🎉 Join me on Bamboo and start earning money by completing simple tasks!

💰 Use my invitation code: ${code}

✅ Easy daily tasks
✅ Instant commissions  
✅ VIP benefits
✅ Team bonuses

Invited by: ${username}

Download now and start earning!`;
    
    navigator.clipboard.writeText(shareText).then(() => {
        showToast('Share text copied to clipboard!', 'success');
        window.shareModal.hide();
    }).catch(() => {
        showToast('Failed to copy text. Please try again.', 'error');
    });
}

function exportTeamData() {
    // Create CSV data
    const csvData = generateTeamCSV();
    
    // Create and download file
    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `team_data_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    showToast('Team data exported successfully!', 'success');
}

function generateTeamCSV() {
    const headers = ['Username', 'Phone', 'VIP Level', 'Balance', 'Commission Generated', 'Join Date', 'Status'];
    let csv = headers.join(',') + '\n';
    
    // Get table data
    const tableRows = document.querySelectorAll('.table tbody tr');
    tableRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
            const rowData = [
                cells[0].querySelector('.fw-bold').textContent.trim(),
                cells[0].querySelector('.text-muted').textContent.trim(),
                cells[1].textContent.trim(),
                cells[2].textContent.trim(),
                cells[3].textContent.trim(),
                cells[4].querySelector('div').textContent.trim(),
                cells[5].textContent.trim()
            ];
            csv += rowData.map(field => `"${field}"`).join(',') + '\n';
        }
    });
    
    return csv;
}

function refreshTeamStats() {
    fetch('get_team_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update stats cards
                updateStatsCards(data.stats);
                
                // Update team chart if needed
                if (data.levelStats) {
                    updateTeamChart(data.levelStats);
                }
                
                showToast('Team statistics updated', 'info');
            }
        })
        .catch(error => {
            console.error('Error refreshing team stats:', error);
        });
}

function updateStatsCards(stats) {
    // Update total team
    const totalTeamElement = document.querySelector('.stats-card:nth-child(1) h3');
    if (totalTeamElement && stats.total_referrals !== undefined) {
        totalTeamElement.textContent = stats.total_referrals;
        animateNumber(totalTeamElement, stats.total_referrals);
    }
    
    // Update direct referrals
    const directReferralsElement = document.querySelector('.stats-card:nth-child(2) h3');
    if (directReferralsElement && stats.direct_referrals !== undefined) {
        directReferralsElement.textContent = stats.direct_referrals;
        animateNumber(directReferralsElement, stats.direct_referrals);
    }
    
    // Update total commission
    const totalCommissionElement = document.querySelector('.stats-card:nth-child(3) h3');
    if (totalCommissionElement && stats.total_team_commission !== undefined) {
        totalCommissionElement.textContent = '$' + parseFloat(stats.total_team_commission).toFixed(2);
    }
    
    // Update this month commission
    const monthCommissionElement = document.querySelector('.stats-card:nth-child(4) h3');
    if (monthCommissionElement && stats.this_month_commission !== undefined) {
        monthCommissionElement.textContent = '$' + parseFloat(stats.this_month_commission).toFixed(2);
    }
}

function animateNumber(element, targetNumber) {
    const startNumber = parseInt(element.textContent) || 0;
    const duration = 1000;
    const startTime = Date.now();
    
    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);
        
        element.textContent = currentNumber;
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    updateNumber();
}

function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 8000 : 5000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Add custom CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInDown {
        from { transform: translate3d(0, -100%, 0); opacity: 0; }
        to { transform: translate3d(0, 0, 0); opacity: 1; }
    }
    
    @keyframes numberPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); color: #28a745; }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

// Export functions for global access
window.copyInvitationCode = copyInvitationCode;
window.shareInvitationCode = shareInvitationCode;
window.shareViaWhatsApp = shareViaWhatsApp;
window.shareViaFacebook = shareViaFacebook;
window.shareViaTwitter = shareViaTwitter;
window.copyShareText = copyShareText;
window.exportTeamData = exportTeamData;
