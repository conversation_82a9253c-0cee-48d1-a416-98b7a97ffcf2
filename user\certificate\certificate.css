/* Bamboo Certificate Display Page Styles */

:root {
    --primary-color: #28a745;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 12px;
    --box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --gold-color: #ffd700;
    --bronze-color: #cd7f32;
    --silver-color: #c0c0c0;
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #20c997 100%) !important;
}

.navbar-brand {
    font-weight: 600;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: translateX(-5px);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Status Card */
.status-card {
    border: 3px solid transparent;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.status-card.eligible {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #d4edda 0%, #ffffff 100%);
}

.status-card.not-eligible {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
}

.status-card .card-header {
    background: transparent;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

.status-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.status-icon.success {
    color: var(--success-color);
    animation: pulse-success 2s infinite;
}

.status-icon.warning {
    color: var(--warning-color);
    animation: pulse-warning 2s infinite;
}

@keyframes pulse-success {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: #20c997; }
    100% { transform: scale(1); }
}

@keyframes pulse-warning {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: #e0a800; }
    100% { transform: scale(1); }
}

/* Requirements Card */
.requirements-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.requirement-item {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--info-color);
    transition: var(--transition);
}

.requirement-item:hover {
    transform: translateX(5px);
    box-shadow: var(--box-shadow);
}

.requirement-label {
    font-weight: 500;
    color: var(--dark-color);
}

.requirement-status {
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.9rem;
}

.requirement-status.completed {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    color: white;
}

.requirement-status.pending {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
    color: white;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    margin-top: 0.5rem;
}

.progress-bar {
    border-radius: 4px;
    transition: width 1s ease-in-out;
}

/* Certificate Card */
.certificate-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 3px solid var(--gold-color);
    position: relative;
}

.certificate-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--gold-color) 0%, #fff 50%, var(--gold-color) 100%);
}

.certificate-card .card-header {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%) !important;
    border-bottom: none;
}

.certificate-container {
    padding: 2rem;
    text-align: center;
}

/* PDF Certificate Styles */
.pdf-certificate {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.pdf-preview-large {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    width: 100%;
    max-width: 600px;
}

.pdf-icon-large {
    font-size: 5rem;
    color: var(--danger-color);
    margin-right: 2rem;
}

.pdf-info-large h4 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.certificate-details {
    background: rgba(255, 255, 255, 0.8);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
    text-align: left;
}

.certificate-details p {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* Image Certificate Styles */
.image-certificate {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.certificate-image-container {
    position: relative;
    max-width: 600px;
    width: 100%;
    margin-bottom: 2rem;
}

.certificate-image {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.certificate-image:hover {
    transform: scale(1.02);
    box-shadow: 0 0.75rem 3rem rgba(0, 0, 0, 0.3);
}

.certificate-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    opacity: 0;
    transition: var(--transition);
}

.certificate-image-container:hover .certificate-overlay {
    opacity: 1;
}

.certificate-details-overlay h4 {
    margin-bottom: 0.5rem;
    color: var(--gold-color);
}

.certificate-details-overlay p {
    margin-bottom: 0.25rem;
}

/* Certificate Actions */
.certificate-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.certificate-actions .btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    min-width: 150px;
}

.certificate-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
}

/* Achievements Card */
.achievements-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.achievement-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius);
    transition: var(--transition);
    height: 100%;
}

.achievement-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.achievement-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    color: var(--primary-color);
    flex-shrink: 0;
}

.achievement-details h5 {
    margin-bottom: 0.25rem;
    color: var(--dark-color);
    font-weight: 700;
}

.achievement-details p {
    margin-bottom: 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Next Steps Card */
.next-steps-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.next-steps-card .card-header {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%) !important;
}

.next-steps-card ul li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.next-steps-card ul li:last-child {
    border-bottom: none;
}

/* Share Modal */
.share-buttons .btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.share-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 50px;
    font-weight: 500;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.6s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.scale-in {
    animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Certificate shine effect */
.certificate-card:hover::before {
    animation: shine 2s ease-in-out;
}

@keyframes shine {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .status-icon {
        font-size: 3rem;
    }
    
    .requirement-item {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
    
    .certificate-container {
        padding: 1rem;
    }
    
    .pdf-preview-large {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }
    
    .pdf-icon-large {
        margin-right: 0;
        margin-bottom: 1rem;
        font-size: 4rem;
    }
    
    .certificate-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .certificate-actions .btn {
        min-width: auto;
        width: 100%;
    }
    
    .achievement-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem 0.5rem;
    }
    
    .achievement-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
        font-size: 2rem;
    }
    
    .requirement-status {
        font-size: 0.8rem;
        padding: 0.2rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .status-icon {
        font-size: 2.5rem;
    }
    
    .pdf-icon-large {
        font-size: 3rem;
    }
    
    .achievement-icon {
        font-size: 1.8rem;
    }
    
    .certificate-details {
        padding: 0.75rem;
    }
    
    .share-buttons .btn {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .status-card {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        color: #ffffff;
    }
    
    .requirements-card {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        color: #ffffff;
    }
    
    .requirement-item {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }
    
    .achievement-item {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }
    
    .certificate-details {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }
    
    .pdf-preview-large {
        background: linear-gradient(135deg, #3d3d3d 0%, #4d4d4d 100%);
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }
    
    .navbar,
    .btn,
    .modal,
    .share-buttons,
    .next-steps-card {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
        break-inside: avoid;
    }
    
    .certificate-card {
        background: white !important;
        color: black !important;
    }
    
    .certificate-image {
        max-width: 100%;
        height: auto;
    }
}
