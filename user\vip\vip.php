<?php
/**
 * Bamboo Web Application - User VIP Levels Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Get app settings for theme colors
$app_name = getAppSetting('app_name', 'Bamboo');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Get all VIP levels
$vip_levels = fetchAll("SELECT * FROM vip_levels ORDER BY level ASC");

// Get user's current VIP level details
$current_vip = fetchRow("SELECT * FROM vip_levels WHERE level = ?", [$user['vip_level']]);

// Calculate next VIP level requirements
$next_vip = fetchRow("SELECT * FROM vip_levels WHERE level = ? ORDER BY level ASC LIMIT 1", [$user['vip_level'] + 1]);

// Get user statistics for VIP progress
$user_stats = [
    'total_balance' => $user['balance'] + $user['commission_balance'],
    'total_deposited' => $user['total_deposited'],
    'total_commission_earned' => $user['total_commission_earned'],
    'tasks_completed_today' => $user['tasks_completed_today'],
    'referral_count' => $user['referral_count']
];

$page_title = "VIP Levels";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bamboo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="vip.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --primary-rgb: <?php echo implode(',', sscanf($primary_color, "#%02x%02x%02x")); ?>;
            --secondary-rgb: <?php echo implode(',', sscanf($secondary_color, "#%02x%02x%02x")); ?>;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/dashboard.php">
                <i class="bi bi-arrow-left me-2"></i>VIP Levels
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bi bi-star-fill me-1 text-warning"></i>
                    Current: <?php echo htmlspecialchars($current_vip['name']); ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Current VIP Status Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card current-vip-card shadow-lg">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3 text-center">
                                <div class="vip-icon-container">
                                    <?php if ($current_vip['icon_path']): ?>
                                        <img src="../../uploads/vip_icons/<?php echo htmlspecialchars($current_vip['icon_path']); ?>" 
                                             alt="<?php echo htmlspecialchars($current_vip['name']); ?>" 
                                             class="vip-icon">
                                    <?php else: ?>
                                        <div class="vip-icon-placeholder">
                                            <i class="bi bi-star-fill"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <h4 class="text-primary mt-3"><?php echo htmlspecialchars($current_vip['name']); ?></h4>
                                <p class="text-muted mb-0">Your Current Level</p>
                            </div>
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-muted mb-3">Current Benefits</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="bi bi-check-circle text-success me-2"></i>Daily Tasks: <strong><?php echo $current_vip['max_daily_tasks']; ?></strong></li>
                                            <li><i class="bi bi-check-circle text-success me-2"></i>Commission Rate: <strong><?php echo number_format($current_vip['commission_multiplier'] * 100, 0); ?>%</strong></li>
                                            <li><i class="bi bi-check-circle text-success me-2"></i>Daily Withdrawal: <strong>$<?php echo number_format($current_vip['withdrawal_limit_daily'], 2); ?></strong></li>
                                            <li><i class="bi bi-check-circle text-success me-2"></i>Withdrawal Fee: <strong><?php echo number_format($current_vip['withdrawal_fee_percentage'], 1); ?>%</strong></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-muted mb-3">Your Progress</h6>
                                        <div class="progress-item">
                                            <div class="d-flex justify-content-between">
                                                <span>Total Balance</span>
                                                <span class="fw-bold">$<?php echo number_format($user_stats['total_balance'], 2); ?></span>
                                            </div>
                                        </div>
                                        <div class="progress-item">
                                            <div class="d-flex justify-content-between">
                                                <span>Total Deposited</span>
                                                <span class="fw-bold">$<?php echo number_format($user_stats['total_deposited'], 2); ?></span>
                                            </div>
                                        </div>
                                        <div class="progress-item">
                                            <div class="d-flex justify-content-between">
                                                <span>Commission Earned</span>
                                                <span class="fw-bold text-success">$<?php echo number_format($user_stats['total_commission_earned'], 2); ?></span>
                                            </div>
                                        </div>
                                        <div class="progress-item">
                                            <div class="d-flex justify-content-between">
                                                <span>Referrals</span>
                                                <span class="fw-bold"><?php echo $user_stats['referral_count']; ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Next VIP Level Progress -->
        <?php if ($next_vip): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card next-vip-card">
                        <div class="card-header bg-gradient-warning text-white">
                            <h6 class="mb-0">
                                <i class="bi bi-arrow-up-circle me-2"></i>
                                Next Level: <?php echo htmlspecialchars($next_vip['name']); ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-3">Upgrade Requirements</h6>
                                    <div class="requirement-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>Minimum Balance Required</span>
                                            <span class="fw-bold">$<?php echo number_format($next_vip['min_balance'], 2); ?></span>
                                        </div>
                                        <div class="progress mt-2">
                                            <?php 
                                            $progress = min(100, ($user_stats['total_balance'] / $next_vip['min_balance']) * 100);
                                            $progress_class = $progress >= 100 ? 'bg-success' : 'bg-warning';
                                            ?>
                                            <div class="progress-bar <?php echo $progress_class; ?>" style="width: <?php echo $progress; ?>%"></div>
                                        </div>
                                        <small class="text-muted">
                                            <?php if ($progress >= 100): ?>
                                                <i class="bi bi-check-circle text-success"></i> Requirement met!
                                            <?php else: ?>
                                                Need $<?php echo number_format($next_vip['min_balance'] - $user_stats['total_balance'], 2); ?> more
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-3">Upgrade Benefits</h6>
                                    <div class="upgrade-benefits">
                                        <div class="benefit-comparison">
                                            <span>Daily Tasks:</span>
                                            <span class="current"><?php echo $current_vip['max_daily_tasks']; ?></span>
                                            <i class="bi bi-arrow-right mx-2"></i>
                                            <span class="upgrade text-success fw-bold"><?php echo $next_vip['max_daily_tasks']; ?></span>
                                        </div>
                                        <div class="benefit-comparison">
                                            <span>Commission Rate:</span>
                                            <span class="current"><?php echo number_format($current_vip['commission_multiplier'] * 100, 0); ?>%</span>
                                            <i class="bi bi-arrow-right mx-2"></i>
                                            <span class="upgrade text-success fw-bold"><?php echo number_format($next_vip['commission_multiplier'] * 100, 0); ?>%</span>
                                        </div>
                                        <div class="benefit-comparison">
                                            <span>Daily Withdrawal:</span>
                                            <span class="current">$<?php echo number_format($current_vip['withdrawal_limit_daily'], 0); ?></span>
                                            <i class="bi bi-arrow-right mx-2"></i>
                                            <span class="upgrade text-success fw-bold">$<?php echo number_format($next_vip['withdrawal_limit_daily'], 0); ?></span>
                                        </div>
                                        <div class="benefit-comparison">
                                            <span>Withdrawal Fee:</span>
                                            <span class="current"><?php echo number_format($current_vip['withdrawal_fee_percentage'], 1); ?>%</span>
                                            <i class="bi bi-arrow-right mx-2"></i>
                                            <span class="upgrade text-success fw-bold"><?php echo number_format($next_vip['withdrawal_fee_percentage'], 1); ?>%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php if ($progress >= 100): ?>
                                <div class="text-center mt-3">
                                    <button class="btn btn-success btn-lg" onclick="requestVIPUpgrade()">
                                        <i class="bi bi-star-fill me-2"></i>Request VIP Upgrade
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="text-center mt-3">
                                    <a href="../deposit/deposit.php" class="btn btn-primary btn-lg">
                                        <i class="bi bi-plus-circle me-2"></i>Make Deposit to Upgrade
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- All VIP Levels Comparison -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="bi bi-list-stars me-2"></i>
                            All VIP Levels Comparison
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="row g-0">
                            <?php foreach ($vip_levels as $index => $vip): ?>
                                <div class="col-lg-<?php echo count($vip_levels) <= 3 ? '4' : (count($vip_levels) <= 4 ? '3' : '2'); ?> col-md-6">
                                    <div class="vip-level-card <?php echo $vip['level'] == $user['vip_level'] ? 'current-level' : ''; ?> <?php echo $vip['level'] < $user['vip_level'] ? 'completed-level' : ''; ?>">
                                        <div class="vip-level-header">
                                            <?php if ($vip['icon_path']): ?>
                                                <img src="../../uploads/vip_icons/<?php echo htmlspecialchars($vip['icon_path']); ?>" 
                                                     alt="<?php echo htmlspecialchars($vip['name']); ?>" 
                                                     class="vip-level-icon">
                                            <?php else: ?>
                                                <div class="vip-level-icon-placeholder">
                                                    <i class="bi bi-star-fill"></i>
                                                </div>
                                            <?php endif; ?>
                                            <h5 class="vip-level-name"><?php echo htmlspecialchars($vip['name']); ?></h5>
                                            <?php if ($vip['level'] == $user['vip_level']): ?>
                                                <span class="badge bg-success">Current</span>
                                            <?php elseif ($vip['level'] < $user['vip_level']): ?>
                                                <span class="badge bg-secondary">Completed</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="vip-level-body">
                                            <div class="vip-requirement">
                                                <strong>Min Balance: $<?php echo number_format($vip['min_balance'], 2); ?></strong>
                                            </div>
                                            <ul class="vip-benefits">
                                                <li><i class="bi bi-check2"></i> <?php echo $vip['max_daily_tasks']; ?> Daily Tasks</li>
                                                <li><i class="bi bi-check2"></i> <?php echo number_format($vip['commission_multiplier'] * 100, 0); ?>% Commission Rate</li>
                                                <li><i class="bi bi-check2"></i> $<?php echo number_format($vip['withdrawal_limit_daily'], 0); ?> Daily Withdrawal</li>
                                                <li><i class="bi bi-check2"></i> <?php echo number_format($vip['withdrawal_fee_percentage'], 1); ?>% Withdrawal Fee</li>
                                            </ul>
                                            <?php if ($vip['benefits']): ?>
                                                <div class="vip-description">
                                                    <small class="text-muted"><?php echo htmlspecialchars($vip['benefits']); ?></small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- VIP Benefits Information -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card info-card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            How VIP Levels Work
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Upgrade Requirements</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-dot"></i> Maintain minimum balance requirement</li>
                                    <li><i class="bi bi-dot"></i> Complete daily tasks consistently</li>
                                    <li><i class="bi bi-dot"></i> Build your referral network</li>
                                    <li><i class="bi bi-dot"></i> Contact support for manual upgrade</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>VIP Benefits</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-dot"></i> Higher commission rates on tasks</li>
                                    <li><i class="bi bi-dot"></i> Increased daily task limits</li>
                                    <li><i class="bi bi-dot"></i> Higher withdrawal limits</li>
                                    <li><i class="bi bi-dot"></i> Lower withdrawal fees</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- VIP Upgrade Request Modal -->
    <div class="modal fade" id="upgradeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Request VIP Upgrade</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>You meet the requirements for <strong><?php echo $next_vip['name'] ?? ''; ?></strong>!</p>
                    <p>Your upgrade request will be reviewed by our team and processed within 24 hours.</p>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Note:</strong> VIP upgrades are processed manually to ensure account security and compliance.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="submitUpgradeRequest()">
                        <i class="bi bi-send me-2"></i>Submit Request
                    </button>
                </div>
            </div>
        </div>
    </div>

<?php
// Set additional JS for footer
$additional_js = '
    <script src="vip.js"></script>
';
include '../includes/user_footer.php';
?>
