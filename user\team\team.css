/* Bamboo Team Management Page Styles */

:root {
    --primary-color: #28a745;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 12px;
    --box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    background: #ffffff;
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%) !important;
}

.navbar-brand {
    font-weight: 600;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: translateX(-5px);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Stats Cards */
.stats-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.2);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%) !important;
}

/* Invitation Card */
.invitation-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid var(--primary-color);
    position: relative;
}

.invitation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, #20c997 50%, var(--primary-color) 100%);
}

.invitation-code-display {
    margin-bottom: 1rem;
}

.invitation-code-display .form-control {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 1.2rem;
    text-align: center;
    background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
    border: 2px solid var(--primary-color);
}

.commission-rates {
    background: rgba(40, 167, 69, 0.05);
    padding: 1rem;
    border-radius: var(--border-radius);
    border-left: 2px solid var(--success-color);
}

.rate-item {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.rate-item:last-child {
    margin-bottom: 0;
}

.rate-item .level {
    flex: 1;
    font-weight: 500;
}

.rate-item .rate {
    font-size: 1.1rem;
    font-weight: 700;
}

/* Level Statistics */
.level-stat-item {
    padding: 1rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: var(--border-radius);
    border-left: 2px solid var(--primary-color);
    transition: var(--transition);
}

.level-stat-item:hover {
    transform: translateX(5px);
    box-shadow: var(--box-shadow);
}

.level-stat-item:last-child {
    margin-bottom: 0;
}

.level-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
}

.level-badge.level-1 {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.level-badge.level-2 {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
}

.level-badge.level-3 {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
}

.level-details {
    margin-top: 0.25rem;
}

.commission-amount {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--success-color);
}

.progress {
    height: 4px;
    border-radius: 2px;
    background-color: #e9ecef;
}

.progress-bar.level-1 {
    background: linear-gradient(90deg, var(--success-color) 0%, #20c997 100%);
}

.progress-bar.level-2 {
    background: linear-gradient(90deg, var(--info-color) 0%, #138496 100%);
}

.progress-bar.level-3 {
    background: linear-gradient(90deg, var(--warning-color) 0%, #e0a800 100%);
}

/* Avatar Circle */
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, #20c997 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

/* Table Styles */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    border-top: none;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.table td {
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table-hover tbody tr:hover {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(32, 201, 151, 0.05) 100%);
    transform: scale(1.01);
    transition: var(--transition);
}

/* Tips Card */
.tips-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
}

.tips-card .card-header {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%) !important;
}

/* Share Modal */
.invitation-code-large {
    font-family: 'Courier New', monospace;
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 2px solid var(--primary-color);
    text-align: center;
    margin-bottom: 1rem;
}

.share-buttons .btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.share-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 50px;
    font-weight: 500;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%) !important;
}

.bg-primary {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%) !important;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #20c997 100%);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #218838 0%, #1aa179 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1aa179 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.6s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.scale-in {
    animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Pulse animation for stats cards */
.stats-card:hover .bi {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .stats-card .card-body {
        padding: 1rem;
    }
    
    .stats-card h3 {
        font-size: 1.5rem;
    }
    
    .invitation-code-display .form-control {
        font-size: 1rem;
    }
    
    .commission-rates {
        margin-top: 1rem;
    }
    
    .rate-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .rate-item .rate {
        margin-top: 0.25rem;
    }
    
    .level-stat-item {
        padding: 0.75rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .avatar-circle {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }
    
    .invitation-code-large {
        font-size: 1.5rem;
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .stats-card .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-card .bi {
        margin-bottom: 0.5rem;
        font-size: 2rem !important;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .level-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }
    
    .commission-amount {
        font-size: 1rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
    
    .share-buttons .btn {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .invitation-card {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        color: #ffffff;
    }
    
    .level-stat-item {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        color: #ffffff;
    }
    
    .tips-card {
        background: linear-gradient(135deg, #2a3a4a 0%, #2d2d2d 100%);
        color: #ffffff;
    }
    
    .table th {
        background: linear-gradient(135deg, #3d3d3d 0%, #4d4d4d 100%);
        color: #ffffff;
    }
    
    .table-hover tbody tr:hover {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(32, 201, 151, 0.2) 100%);
    }
    
    .invitation-code-display .form-control {
        background: linear-gradient(135deg, #3d3d3d 0%, #2d2d2d 100%);
        color: #ffffff;
        border-color: var(--primary-color);
    }
    
    .invitation-code-large {
        background: linear-gradient(135deg, #3d3d3d 0%, #2d2d2d 100%);
        color: var(--primary-color);
    }
    
    .commission-rates {
        background: rgba(40, 167, 69, 0.2);
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }
    
    .navbar,
    .btn,
    .modal,
    .share-buttons {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
        break-inside: avoid;
    }
    
    .stats-card {
        background: white !important;
        color: black !important;
    }
    
    .table {
        font-size: 0.8rem;
    }
}
