<?php
/**
 * Bamboo Web Application - User Profile Management
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Get VIP level information
$vip_level = fetchRow("SELECT * FROM vip_levels WHERE level = ?", [$user['vip_level']]);

// Get app settings
$app_name = getAppSetting('app_name', 'Bamboo');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Handle POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $username = trim($_POST['username'] ?? '');
                $email = trim($_POST['email'] ?? '');
                $phone = trim($_POST['phone'] ?? '');
                $gender = $_POST['gender'] ?? '';
                
                // Validate inputs
                if (empty($username) || empty($phone) || empty($gender)) {
                    showError('Username, phone, and gender are required.');
                    break;
                }
                
                // Check if username/phone/email are already taken by other users
                $existing_user = fetchRow("SELECT id FROM users WHERE (username = ? OR phone = ? OR (email = ? AND email IS NOT NULL)) AND id != ?", [$username, $phone, $email, $user_id]);
                if ($existing_user) {
                    showError('Username, phone, or email already exists.');
                    break;
                }
                
                $update_data = [
                    'username' => $username,
                    'phone' => $phone,
                    'gender' => $gender
                ];
                
                if (!empty($email)) {
                    $update_data['email'] = $email;
                }
                
                if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                    showSuccess('Profile updated successfully!');
                    // Refresh user data
                    $user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
                } else {
                    showError('Failed to update profile.');
                }
                break;
                
            case 'change_password':
                $current_password = $_POST['current_password'] ?? '';
                $new_password = $_POST['new_password'] ?? '';
                $confirm_password = $_POST['confirm_password'] ?? '';
                
                // Validate inputs
                if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                    showError('All password fields are required.');
                    break;
                }
                
                if ($new_password !== $confirm_password) {
                    showError('New passwords do not match.');
                    break;
                }
                
                if (strlen($new_password) < 6) {
                    showError('New password must be at least 6 characters long.');
                    break;
                }
                
                // Verify current password
                if (!password_verify($current_password, $user['password_hash'])) {
                    showError('Current password is incorrect.');
                    break;
                }
                
                // Update password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                if (updateRecord('users', ['password_hash' => $hashed_password], 'id = ?', [$user_id])) {
                    showSuccess('Password changed successfully!');
                } else {
                    showError('Failed to change password.');
                }
                break;
                
            case 'set_withdrawal_pin':
                $new_pin = $_POST['new_pin'] ?? '';
                $confirm_pin = $_POST['confirm_pin'] ?? '';
                
                // Validate inputs
                if (empty($new_pin) || empty($confirm_pin)) {
                    showError('Both PIN fields are required.');
                    break;
                }
                
                if ($new_pin !== $confirm_pin) {
                    showError('PINs do not match.');
                    break;
                }
                
                if (strlen($new_pin) < 4) {
                    showError('PIN must be at least 4 characters long.');
                    break;
                }
                
                // Update withdrawal PIN
                $hashed_pin = password_hash($new_pin, PASSWORD_DEFAULT);
                if (updateRecord('users', ['withdrawal_pin_hash' => $hashed_pin], 'id = ?', [$user_id])) {
                    showSuccess('Withdrawal PIN set successfully!');
                    // Refresh user data
                    $user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
                } else {
                    showError('Failed to set withdrawal PIN.');
                }
                break;
                
            case 'update_wallet_info':
                $wallet_address = trim($_POST['wallet_address'] ?? '');
                $exchange_name = trim($_POST['exchange_name'] ?? '');
                
                if (empty($wallet_address)) {
                    showError('Wallet address is required.');
                    break;
                }
                
                $update_data = [
                    'wallet_address' => $wallet_address,
                    'exchange_name' => $exchange_name
                ];
                
                if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                    showSuccess('Wallet information updated successfully!');
                    // Refresh user data
                    $user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
                } else {
                    showError('Failed to update wallet information.');
                }
                break;
        }
    }
}

// Check if user has withdrawal PIN set
$has_withdrawal_pin = !empty($user['withdrawal_pin_hash']);

// Set page title for header
$page_title = 'Profile Management';

// Include header
include '../includes/user_header.php';
?>

            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <a href="<?php echo BASE_URL; ?>user/dashboard/dashboard.php" class="btn btn-outline-primary me-3">
                            <i class="bi bi-arrow-left"></i>
                        </a>
                        <div>
                            <h4 class="mb-0">Profile Management</h4>
                            <small class="text-muted">Manage your account settings and information</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Information Cards Row -->
            <div class="row mb-4">
                <!-- Profile Overview Card -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="user-avatar-large mb-3">
                                <?php if ($user['avatar_url']): ?>
                                    <img src="<?php echo BASE_URL . $user['avatar_url']; ?>" alt="Avatar" class="rounded-circle" style="width: 80px; height: 80px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="avatar-initials-large bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                                        <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <h5 class="card-title"><?php echo htmlspecialchars($user['username']); ?></h5>
                            <span class="badge bg-primary mb-3">VIP <?php echo $user['vip_level']; ?></span>

                            <div class="profile-stats">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">Balance:</span>
                                    <strong class="text-success">USDT <?php echo number_format($user['balance'], 2); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">Credit Score:</span>
                                    <strong><?php echo $user['credit_score'] ?? 100; ?></strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">Referral Code:</span>
                                    <div class="d-flex align-items-center">
                                        <strong class="me-2"><?php echo htmlspecialchars($user['invitation_code']); ?></strong>
                                        <button class="btn btn-sm btn-outline-primary" onclick="copyReferralCode()" title="Copy referral code">
                                            <i class="bi bi-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span class="text-muted">Member Since:</span>
                                    <strong><?php echo date('M Y', strtotime($user['created_at'])); ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Status Card -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-shield-check me-2 text-success"></i>Account Status
                            </h5>
                            <div class="status-items">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Account Verified:</span>
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>Verified
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Withdrawal PIN:</span>
                                    <?php if ($has_withdrawal_pin): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-lock me-1"></i>Set
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">
                                            <i class="bi bi-unlock me-1"></i>Not Set
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Wallet Address:</span>
                                    <?php if (!empty($user['wallet_address'])): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-wallet2 me-1"></i>Set
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">
                                            <i class="bi bi-wallet2 me-1"></i>Not Set
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Account Type:</span>
                                    <span class="badge bg-primary">Standard</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats Card -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-graph-up me-2 text-primary"></i>Quick Stats
                            </h5>
                            <div class="stats-grid">
                                <div class="stat-item text-center mb-3">
                                    <div class="stat-number text-primary h4">USDT <?php echo number_format($user['total_deposited'] ?? 0, 2); ?></div>
                                    <div class="stat-label text-muted">Total Deposited</div>
                                </div>
                                <div class="stat-item text-center mb-3">
                                    <div class="stat-number text-success h4">USDT <?php echo number_format($user['total_withdrawn'] ?? 0, 2); ?></div>
                                    <div class="stat-label text-muted">Total Withdrawn</div>
                                </div>
                                <div class="stat-item text-center">
                                    <div class="stat-number text-info h4"><?php echo $user['total_referrals'] ?? 0; ?></div>
                                    <div class="stat-label text-muted">Total Referrals</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Cards Row -->
            <div class="row mb-4">
                <!-- Financial Actions Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-credit-card me-2 text-success"></i>Financial Actions
                            </h5>
                            <p class="text-muted">Manage your deposits, withdrawals, and transactions</p>
                            <div class="row g-2">
                                <div class="col-6">
                                    <a href="<?php echo BASE_URL; ?>user/deposit/deposit.php" class="btn btn-outline-success w-100">
                                        <i class="bi bi-plus-circle me-2"></i>Deposit
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="<?php echo BASE_URL; ?>user/withdraw/withdraw.php" class="btn btn-outline-warning w-100">
                                        <i class="bi bi-dash-circle me-2"></i>Withdraw
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="<?php echo BASE_URL; ?>user/transactions/transactions.php" class="btn btn-outline-info w-100">
                                        <i class="bi bi-list-ul me-2"></i>Transactions
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="<?php echo BASE_URL; ?>user/vip/vip.php" class="btn btn-outline-primary w-100">
                                        <i class="bi bi-star me-2"></i>VIP Levels
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Platform Actions Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-grid-3x3-gap me-2 text-primary"></i>Platform Actions
                            </h5>
                            <p class="text-muted">Access platform features and tools</p>
                            <div class="row g-2">
                                <div class="col-6">
                                    <a href="<?php echo BASE_URL; ?>user/tasks/tasks.php" class="btn btn-outline-primary w-100">
                                        <i class="bi bi-list-task me-2"></i>Tasks
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="<?php echo BASE_URL; ?>user/team/team.php" class="btn btn-outline-info w-100">
                                        <i class="bi bi-people me-2"></i>Team
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="<?php echo BASE_URL; ?>user/certificate/certificate.php" class="btn btn-outline-warning w-100">
                                        <i class="bi bi-award me-2"></i>Certificate
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="<?php echo BASE_URL; ?>user/dashboard/dashboard.php" class="btn btn-outline-secondary w-100">
                                        <i class="bi bi-house me-2"></i>Dashboard
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Cards Row -->
            <div class="row">
                <!-- Profile Management -->
                <div class="col-lg-8 mb-4">

                <!-- Personal Information Card -->
                <div class="col-lg-8 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-person-lines-fill me-2"></i>Personal Information
                            </h5>
                            <form action="profile.php" method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_profile">

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" name="username"
                                               value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               value="<?php echo htmlspecialchars($user['phone']); ?>" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email (Optional)</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="gender" class="form-label">Gender</label>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="male" <?php echo $user['gender'] === 'male' ? 'selected' : ''; ?>>Male</option>
                                            <option value="female" <?php echo $user['gender'] === 'female' ? 'selected' : ''; ?>>Female</option>
                                        </select>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>Update Profile
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Quick Settings Card -->
                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-gear me-2"></i>Quick Settings
                            </h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="showPasswordModal()">
                                    <i class="bi bi-key me-2"></i>Change Password
                                </button>
                                <button class="btn btn-outline-warning" onclick="showPinModal()">
                                    <i class="bi bi-shield-lock me-2"></i>
                                    <?php echo $has_withdrawal_pin ? 'Update' : 'Set'; ?> PIN
                                </button>
                                <button class="btn btn-outline-success" onclick="showWalletModal()">
                                    <i class="bi bi-wallet2 me-2"></i>Update Wallet
                                </button>
                                <a href="<?php echo BASE_URL; ?>user/login/logout.php" class="btn btn-outline-danger">
                                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Information Links Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-info-circle me-2 text-info"></i>Information & Support
                            </h5>
                            <p class="text-muted mb-3">Access important information and support resources</p>
                            <div class="row g-2">
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-outline-primary w-100" data-modal="terms">
                                        <i class="bi bi-file-text me-2"></i>Terms & Conditions
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-outline-info w-100" data-modal="faq">
                                        <i class="bi bi-question-circle me-2"></i>FAQ
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-outline-success w-100" data-modal="campaign">
                                        <i class="bi bi-megaphone me-2"></i>Latest Campaign
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Settings Modal Cards -->
            <div class="row">
                <!-- Change Password Card -->
                <div class="col-lg-6 mb-4" id="passwordCard" style="display: none;">
                    <div class="card shadow-sm border-warning">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-key me-2 text-warning"></i>Change Login Password
                            </h5>
                            <form action="profile.php" method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="change_password">

                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                                </div>
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-key me-2"></i>Change Password
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="hidePasswordModal()">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal PIN Card -->
                <div class="col-lg-6 mb-4" id="pinCard" style="display: none;">
                    <div class="card shadow-sm border-danger">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-shield-lock me-2 text-danger"></i>Withdrawal PIN
                                <?php if ($has_withdrawal_pin): ?>
                                    <span class="badge bg-success ms-2">Set</span>
                                <?php else: ?>
                                    <span class="badge bg-warning ms-2">Not Set</span>
                                <?php endif; ?>
                            </h5>
                            <p class="text-muted small">This PIN is required for all withdrawal requests for security.</p>

                            <form action="profile.php" method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="set_withdrawal_pin">

                                <div class="mb-3">
                                    <label for="new_pin" class="form-label">
                                        <?php echo $has_withdrawal_pin ? 'New' : 'Set'; ?> Withdrawal PIN
                                    </label>
                                    <input type="password" class="form-control" id="new_pin" name="new_pin" required minlength="4">
                                </div>
                                <div class="mb-3">
                                    <label for="confirm_pin" class="form-label">Confirm PIN</label>
                                    <input type="password" class="form-control" id="confirm_pin" name="confirm_pin" required>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="bi bi-shield-check me-2"></i>
                                        <?php echo $has_withdrawal_pin ? 'Update' : 'Set'; ?> PIN
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="hidePinModal()">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Wallet Information Card -->
                <div class="col-lg-12 mb-4" id="walletCard" style="display: none;">
                    <div class="card shadow-sm border-success">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-wallet2 me-2 text-success"></i>Wallet Information
                            </h5>
                            <p class="text-muted small">Set your wallet address for withdrawals.</p>

                            <form action="profile.php" method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_wallet_info">

                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="wallet_address" class="form-label">USDT Wallet Address</label>
                                        <input type="text" class="form-control" id="wallet_address" name="wallet_address"
                                               value="<?php echo htmlspecialchars($user['wallet_address'] ?? ''); ?>"
                                               placeholder="Enter your USDT wallet address" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="exchange_name" class="form-label">Exchange Name (Optional)</label>
                                        <input type="text" class="form-control" id="exchange_name" name="exchange_name"
                                               value="<?php echo htmlspecialchars($user['exchange_name'] ?? ''); ?>"
                                               placeholder="e.g., Binance, Coinbase">
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-success">
                                        <i class="bi bi-check-circle me-2"></i>Update Wallet Info
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="hideWalletModal()">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </main>

    <script>
        // Modal-like card display functions
        function showPasswordModal() {
            hideAllModals();
            document.getElementById('passwordCard').style.display = 'block';
            document.getElementById('passwordCard').scrollIntoView({ behavior: 'smooth' });
        }

        function hidePasswordModal() {
            document.getElementById('passwordCard').style.display = 'none';
        }

        function showPinModal() {
            hideAllModals();
            document.getElementById('pinCard').style.display = 'block';
            document.getElementById('pinCard').scrollIntoView({ behavior: 'smooth' });
        }

        function hidePinModal() {
            document.getElementById('pinCard').style.display = 'none';
        }

        function showWalletModal() {
            hideAllModals();
            document.getElementById('walletCard').style.display = 'block';
            document.getElementById('walletCard').scrollIntoView({ behavior: 'smooth' });
        }

        function hideWalletModal() {
            document.getElementById('walletCard').style.display = 'none';
        }

        function hideAllModals() {
            document.getElementById('passwordCard').style.display = 'none';
            document.getElementById('pinCard').style.display = 'none';
            document.getElementById('walletCard').style.display = 'none';
        }

        // Wait for DOM to load before adding event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Password confirmation validation
            const confirmPasswordField = document.getElementById('confirm_password');
            if (confirmPasswordField) {
                confirmPasswordField.addEventListener('input', function() {
                    const newPassword = document.getElementById('new_password').value;
                    const confirmPassword = this.value;

                    if (newPassword !== confirmPassword) {
                        this.setCustomValidity('Passwords do not match');
                    } else {
                        this.setCustomValidity('');
                    }
                });
            }

            // PIN confirmation validation
            const confirmPinField = document.getElementById('confirm_pin');
            if (confirmPinField) {
                confirmPinField.addEventListener('input', function() {
                    const newPin = document.getElementById('new_pin').value;
                    const confirmPin = this.value;

                    if (newPin !== confirmPin) {
                        this.setCustomValidity('PINs do not match');
                    } else {
                        this.setCustomValidity('');
                    }
                });
            }

            // Copy referral code functionality
            window.copyReferralCode = function() {
                const referralCode = '<?php echo htmlspecialchars($user['invitation_code']); ?>';
                navigator.clipboard.writeText(referralCode).then(function() {
                    // Show success message
                    const toast = document.createElement('div');
                    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
                    toast.style.zIndex = '9999';
                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                Referral code copied to clipboard!
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.show();

                    // Remove toast after it's hidden
                    toast.addEventListener('hidden.bs.toast', function() {
                        document.body.removeChild(toast);
                    });
                }).catch(function() {
                    alert('Failed to copy referral code');
                });
            };
        });
    </script>

<?php
// Set additional JS for footer
$additional_js = '
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
';
include '../includes/user_footer.php';
?>
